<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:info.association_name}]" :current="topCurrent" activeColor="#000"
						:bold="false" :fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="position: relative;">
				<swiper class="card-swiper" :circular="true" :autoplay="true" duration="500" interval="8000"
					@change="cardSwiper" style="height: 370rpx;">
					<swiper-item style="padding: 0px;border-radius: 0;">
						<view class="swiper-item image-banner"
							:style="'background-image:url('+ apiImgUrl+info.association_image + ');background-size: contain;  background-position:center;  background-repeat: no-repeat;border-radius: 0;'">
						</view>
					</swiper-item>
				</swiper>
				<view style="position: absolute;top: 10px;right: 10px;" @click="openQrcode">
					<view
						style="position: relative;;background-color: rgba(255, 255, 255, 0.9);box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.2);">
						<text class="tn-icon-qr-code" style="font-size: 40rpx;"></text>
					</view>
				</view>

			</view>
		</view>
		<view style="padding:20rpx 30rpx;">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between" style="font-size: 30rpx;">
				<view style="min-width: 200rpx;">协会地址</view>
				<view>{{info.association_dizhi}}</view>
			</view>
			<view style="margin: 20rpx 0rpx;font-size: 30rpx;"
				class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between">
				<view style="min-width: 200rpx;">协会邮箱</view>
				<view>{{info.association_emal}}</view>
			</view>
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between" style="font-size: 30rpx;">
				<view style="min-width: 200rpx;">联系方式</view>
				<view>{{info.association_phone}}</view>
			</view>
		</view>
		<view style="height: 10rpx;background-color: #EBF4F7;"></view>
		<view style="padding:10rpx 30rpx 200rpx 30rpx;">
			<tn-tabs :list="list" :isScroll="false" :activeItemStyle="{'fontWeight':'600','fontSize':'30rpx'}"
				style="font-weight: ;" activeColor="#000" :barWidth="50" :barHeight="6"
				:barStyle="{'background': 'linear-gradient(-45deg, #4AA2EF, #3A7FF4)','borderRadius': '4rpx'}"
				:current="current" name="name" @change="change"></tn-tabs>
			<!--			<view style="margin-top: 20rpx;position: relative;">-->
			<!--				<image src="/static/b1.png"-->
			<!--					style="width: 100rpx;height: 100rpx;position: absolute;left: 0;right: 0;top: 38%;z-index: 10;margin: 0 auto;">-->
			<!--				</image>-->
			<!--				<image src="/static/t7.jpg" style="width: 100%;border-radius: 20rpx;" mode="widthFix"></image>-->
			<!--			</view>-->
			<view style="line-height: 50rpx; text-indent: 2em;margin-top: 20rpx;" v-if="current==0">
				<div v-html="info.association_desc"></div>
			</view>
			<view style="line-height: 50rpx; text-indent: 2em;margin-top: 20rpx;" v-if="current==2">
				<div v-html="info.association_guizhang"></div>
			</view>
			<view style="line-height: 50rpx; text-indent: 2em;margin-top: 20rpx;" v-if="current==3">
				<div v-html="info.ruhuixizhu"></div>
			</view>
			<view style="padding: 30rpx;text-align: center; " v-if="current==1">
				<view style="width: 100%;background-color: #ffffff;z-index: 1;">
					<tn-tabs :list="tab_list" :isScroll="false" :current="tab_current" name="name" activeColor="#000000"
						:activeItemStyle="{backgroundColor:'#F2F2F2'}" :showBar='false' @change="tab_change"></tn-tabs>
				</view>
				<view style="padding: 30rpx 0rpx;text-align: center;padding-top: 30rpx;">
					<template v-for="(item, index) in architecture" v-if="item.neirong.length>0">
						<tn-button backgroundColor="#82B2FF" height="50rpx"
							fontColor="#ffffff">{{item.position_name}}</tn-button>
						<tn-grid align="center" col="5" hoverClass="none">
							<tn-grid-item v-if="item.neirong" style="width:20%" v-for="(v,i) in item.neirong"
								@click="openUrl('/pages/packageA/user/my_card?id='+v.member_id)">
								<view style="padding: 30rpx;">
									<image :src="apiImgUrl+v.photo_image"
										style="width: 100rpx;height: 100rpx;border-radius: 50%;">
									</image>
									<view>{{v.nikename}}</view>
								</view>
							</tn-grid-item>
							<view style="height: 30rpx;width: 100%;"></view>
							<tn-grid-item v-if="!item.neirong" style="width:20%">
								<view style="padding: 30rpx;">
									<view>暂无</view>
								</view>
							</tn-grid-item>
						</tn-grid>
					</template>
				</view>
				<!-- <template v-for="(item, index) in architecture">
					<tn-button backgroundColor="#E83A30" height="50rpx" width="250rpx"
						fontColor="#ffffff">{{item.position_name}}</tn-button>
					<tn-grid align="center" col="5">
						<tn-grid-item v-if="item.neirong" style="width:20%" v-for="(v,i) in item.neirong">
							<view style="padding: 30rpx;">
								<image :src="apiImgUrl+v.photo_image"
									style="width: 100rpx;height: 100rpx;border-radius: 50%;">
								</image>
								<view>{{v.nikename}}</view>
							</view>
						</tn-grid-item>
						<tn-grid-item v-if="!item.neirong" style="width:20%">
							<view style="padding: 30rpx;">
								<view>暂无</view>
							</view>
						</tn-grid-item>
					</tn-grid>
				</template> -->
			</view>
		</view>
		<!-- <view style="position: fixed;bottom: 50rpx;width: 100%;">
      <view class="tn-flex tn-flex-row-around"> -->
		<!-- <view @click="openUrl('/pages/index/directory?id='+info.id)"
              style="color: #fff;;letter-spacing: 10rpx;line-height: 70rpx;;text-align: center;;width: 300rpx;height: 70rpx;background: linear-gradient(-45deg, #4AA2EF, #3A7FF4);border-radius: 50rpx;">
          会员名录</view> -->

		<!--   <view v-if="userInfo.association_id!=association_id" @click="openUrl('/pages/index/apply_in')"
              style="color: #fff;;letter-spacing: 10rpx;line-height: 70rpx;;text-align: center;;width: 300rpx;height: 70rpx;background: linear-gradient(270deg, #EE7E45, #EE9657);border-radius: 50rpx;">
          申请入会</view> -->
		<!-- <view v-if="userInfo.association_id!=association_id"
			@click="openUrl('/pages/index/apply_in?association_id='+association_id)"
			style="position: fixed;bottom: 260rpx;right: 20rpx;line-height: 35rpx;color: #fff;;text-align: center;width: 100rpx;height: 100rpx;background: linear-gradient(270deg, #EE7E45, #EE9657);border-radius: 50%;">
			<view style="padding-top: 16rpx;">申请</view>
			<view>入会</view>
		</view>
		<view style="position: fixed;bottom: 250rpx; width: 100%;">
			<view class="tn-flex tn-flex-row-around">
				<view @click="openUrl('/pages/index/directory_back?id='+info.id)"
					style="position: absolute; right:20rpx;color: #fff;  line-height:35rpx;text-align: center;width: 100rpx;height: 100rpx;background: linear-gradient(-45deg, #4AA2EF, #3A7FF4);border-radius: 50%;">
					<view style="padding-top: 16rpx;">会员</view>
					<view>名录</view>
				</view>
			</view>
		</view> -->
		<!-- </view> -->
		<tn-popup :closeBtn="true" v-model="show" mode="center" width="500rpx" height="600rpx">
			<!-- 	<div style="text-align: center;padding: 30rpx;font-size: 32rpx;font-weight: 600;">使用二维码邀请入会</div> -->
			<view style="text-align: center;margin-top: 40rpx;">
				<image :src="qrcode_url" style="width: 400rpx;height: 400rpx;"></image>
			</view>
			<view style="text-align: center;margin-top: 40rpx;">
				<tn-button shape="round" backgroundColor="#82B2FF" fontColor="#ffffff"
					@click="saveBase64">保存图片</tn-button>
			</view>
		</tn-popup>
		<tn-tabbar :outHeight="140" :height="120" v-model="currentIndex" :list="tabbarList" activeColor="#3377FF"
			inactiveColor="#AAAAAA" activeIconColor="#3377FF" inactiveIconColor="#8A8E99" :animation="true"
			:safeAreaInsetBottom="true" @change="switchTabbar"></tn-tabbar>
	</view>

</template>

<script>
	import {
		associationInfo,
		architectureList,
		getUserIndex,
	} from '@/util/api.js';
	import store from "@/store";
	export default {
		data() {
			return {
				show: false,
				currentIndex: -1,
				// 底部tabbar菜单数据
				tabbarList: [{
						title: '首页',
						activeIcon: '/static/01_1.png',
						inactiveIcon: '/static/01.png'
					},
					{
						title: '通讯录',
						activeIcon: '/static/02_2.png',
						inactiveIcon: '/static/02.png'
					},
					{
						title: '发现',
						activeIcon: '/static/03_3.png',
						inactiveIcon: '/static/03.png'
					},
					{
						title: '个人中心',
						activeIcon: '/static/04_4.png',
						inactiveIcon: '/static/04.png'
					}
				],
				topCurrent: 0,
				swiperList: [{
					url: '/static/banner1.jpg',
				}],
				tab_list: [{
					name: '理事会'
				}, {
					name: '监事会'
				}],
				list: [{
					name: '协会简介'
				}, {
					name: '组织架构'
				}, {
					name: '规章制度',
				}, {
					name: '入会须知',
				}],
				current: 0,
				cardCur: 0,
				info: {},
				apiImgUrl: this.$store.state.imgUrl,
				qrcode_url: '',
				architecture: [],
				association_id: 0,
				userInfo: {},
				type: 1,
				tab_current: 0
			}
		},
		onLoad(d) {
			console.log(d);
			this.association_id = d.id;
			this.getAssociationInfo();
			this.getArchitectureList();
		},
		methods: {
			openQrcode() {
				uni.showLoading({
					title: '生成中',
					mask: true,
				});
				var that = this;
				uni.request({
					url: store.state.apiUrl + '/move/login/getUnlimitedQRCode',
					method: 'POST',
					data: {
						path: 'pages/index/index',
						scene: "gid=" + this.association_id
					},
					responseType: 'arraybuffer',
					arraybuffer: true,
					success: (result) => {
						const url = 'data:image/png;base64,' + uni.arrayBufferToBase64(result.data);
						that.base64ToImage(url);
					}
				})
			},
			base64ToImage(base64Data) {
				var that = this;
				const fs = uni.getFileSystemManager();
				var filePath = wx.env.USER_DATA_PATH + '/qrcode.jpg';
				const base64 = base64Data.split(',')[1]; // 获取base64字符串部分
				fs.writeFile({
					filePath: filePath,
					data: base64,
					encoding: 'base64',
					success: (res) => {
						that.qrcode_url = filePath;
						console.log('图片保存成功', filePath);
						// 成功回调
						that.show = true;
						uni.hideLoading()
					},
					fail: (err) => {
						console.error('图片保存失败', err);
						uni.hideLoading()
						// 失败回调
					}
				});
			},
			saveBase64() {
				wx.saveImageToPhotosAlbum({
					filePath: this.qrcode_url,
					success: function(res) {
						wx.showToast({
							title: '保存成功',
						})
					},
					fail: function(err) {
						console.log(err, '失败')
					}
				})
			},
			tab_change(d) {
				this.tab_current = d;
				this.type = d + 1;
				this.architecture = [];
				this.getArchitectureList();
			},
			getArchitectureList() {
				architectureList({
						association_id: this.association_id,
						type: this.type,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.architecture = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getAssociationInfo() {
				console.log('discovery');
				associationInfo({
						association_id: this.association_id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							if (key.association_desc.indexOf("<img") != -1) {
								key.association_desc = getApp().addWidthToImages(key.association_desc);
							}
							this.info = key;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				// var uid = uni.getStorageSync('uid');
				// if (!uid) {
				// 	uni.showToast({
				// 		title: '请登录！',
				// 		icon: 'none',
				// 		duration: 2000
				// 	});
				// 	return;
				// }
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
			switchTabbar(d) {
				console.log(d);
				uni.$emit('depId', {
					index: d
				})
				uni.navigateBack()
			},
			change(e) {
				this.current = e;
			},
		}
	}
</script>

<style>

</style>
