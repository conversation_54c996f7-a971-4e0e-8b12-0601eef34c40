<template>
	<view class="template-edit tn-safe-area-inset-bottom">
		<!-- 顶部自定义导航 -->
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'意见反馈'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>

		<view class="tn-safe-area-inset-bottom" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
				<view class="tn-flex justify-content-item">
					<view class="tn-text-lg tn-padding-right-xs tn-text-bold">反馈人</view>
				</view>
			</view>
			<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
				<input placeholder="填写姓名" name="input" v-model="formData.name" placeholder-style="color:#AAAAAA"></input>
			</view>
			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
				<view class="tn-flex justify-content-item">
					<view class="tn-text-lg tn-padding-right-xs tn-text-bold">联系方式</view>
				</view>
			</view>
			<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
				<input placeholder="填写联系方式"  v-model="formData.phone" name="input" placeholder-style="color:#AAAAAA"></input>
			</view>
			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
				<view class="tn-flex justify-content-item">
					<view class="tn-text-lg tn-padding-right-xs tn-text-bold">反馈内容</view>
				</view>
			</view>
			<view class="tn-margin tn-bg-gray--light tn-padding" style="border-radius: 10rpx;">
				<textarea maxlength="500" v-model="formData.content" placeholder="填写反馈内容" placeholder-style="color:#AAAAAA"></textarea>
			</view>
			<!-- 悬浮按钮-->
			<view class="tn-flex tn-footerfixed">
				<view class="tn-flex-1 justify-content-item tn-margin-sm tn-text-center">
					<tn-button backgroundColor="#FBBD12" fontColor="#ffffff" padding="40rpx 0" width="60%" shadow
						fontBold @tap="submit">
						<!-- <text class="tn-icon-light tn-padding-right-xs tn-color-black"></text> -->
						<text>提 交</text>
					</tn-button>
				</view>
			</view>

		</view>

		<view class='tn-tabbar-height'></view>

	</view>
</template>

<script>
import { messageAdd} from "@/util/api";
import store from "@/store";
	export default {
		data() {
			return {
				topCurrent: 0,
        formData:{
          name:'',
          phone:'',
          content:'',
          association_id:store.state.Gid,
          member_id:uni.getStorageSync('uid'),
          createtime:new Date().toISOString().slice(0, 19).replace('T', ' ')
        }
			}
		},
		onLoad() {

		},
		methods: {
      submit(){
        messageAdd(this.formData)
            .then(res => {
              console.log(res);
              if(res.code==1){
                uni.showToast({
                  title: '提交成功！',
                  icon: 'none',
                  duration: 2000
                });
                this.formData.name='';
                this.formData.phone='';
                this.formData.content='';
              }else{
                uni.showToast({
                  title: res.msg,
                  icon: 'none',
                  duration: 2000
                });
              }
            })
            .catch(error => {
              uni.showToast({
                title: error,
                icon: 'none',
                duration: 2000
              });
            })
      },
			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 底部悬浮按钮 start*/
	.tn-tabbar-height {
		min-height: 100rpx;
		height: calc(120rpx + env(safe-area-inset-bottom) / 2);
	}

	.tn-footerfixed {
		position: fixed;
		width: 100%;
		bottom: calc(30rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);

	}

	/* 底部悬浮按钮 end*/

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 20rpx 20rpx 5rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/
</style>
