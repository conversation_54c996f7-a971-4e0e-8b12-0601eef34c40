<template>
	<view class="message tn-safe-area-inset-bottom">

		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'我的消息'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view class="" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="text-align: right;padding: 30rpx 30rpx 0rpx 0;">
				<tn-button @click="all_upadte" backgroundColor="tn-bg-blue" size="sm" fontColor="tn-color-white"
					shape="round">全部已读</tn-button>
			</view>
			<view v-for="item in list" class="tn-margin tn-padding" @click="tn('/pageB/chat/chatGPT')">
				<view class="tn-flex tn-flex-col-top">
					<!-- 这个是图标形式 -->
					<view class="">
						<view class="icon15__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-shadow-blur"
							style="background-color: #F3F2F7;color: #7C8191;">
							<view class="tn-icon-notice-fill"></view>
						</view>
					</view>
					<view class="tn-padding-left-sm" style="width: 100%;">
						<view class="tn-flex tn-flex-col-center ">
							<view class="justify-content-item">
								<text class="tn-color-wallpaper tn-text-lg tn-text-bold">系统消息</text>
							</view>
							<view style="margin-left: 10rpx;">
								<tn-tag padding="0rpx 0rpx" width="80rpx" v-if="item.state==0" size="sm" shape="circle"
									backgroundColor="#E83A30" fontColor="#ffffff">未读</tn-tag>
								<tn-tag padding="0rpx 0rpx" width="80rpx" v-if="item.state==1" size="sm" shape="circle"
									backgroundColor="#28B93D" fontColor="#ffffff">已读</tn-tag>
							</view>
						</view>
						<view v-if="item.type==1" class="tn-padding-top-xs  tn-text-ellipsis-2 tn-color-gray">
							<view>【{{item.member_q_id_name}}】申请查看您的联系方式！</view>
							<view v-if="item.content!=''" style="margin-top: 10rpx;">申请信息：{{item.content}}</view>
						</view>
						<view v-if="item.type==0" class="tn-padding-top-xs  tn-text-ellipsis-2 tn-color-gray">
							<view style="margin-top: 10rpx;">{{item.content}}</view>
						</view>
					</view>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-between tn-margin-top-sm tn-flex-col-center">
					<view class="justify-content-item tn-color-gray tn-text-center tn-color-gray">
						<text class="tn-icon-time tn-padding-right-xs tn-padding-left-xs tn-text-df"></text>
						<text class="tn-text-sm">{{item.mail_time}}</text>
					</view>
					<block v-if="item.type==1 && item.if_tongyi==2">
						<view @click="updateType(0,item.mail_id)"
							class="justify-content-item tn-round tn-text tn-bg-blue--light tn-color-blue"
							style="padding: 5rpx 15rpx;">
							<text class="tn-padding-xs">通过申请</text>
						</view>
						<view @click="updateType(1,item.mail_id)"
							class="justify-content-item tn-round tn-text tn-bg-orangered--light tn-color-orangered"
							style="padding: 5rpx 15rpx;">
							<text class="tn-padding-xs">拒绝申请</text>
						</view>
					</block>
					<block v-if="item.type==1 && item.if_tongyi==1">
						<view>
							<tn-tag padding="0rpx 0rpx" width="80rpx" size="sm" shape="circle" backgroundColor="#E83A30"
								fontColor="#ffffff">已拒绝</tn-tag>
						</view>
					</block>
					<block v-if="item.type==1 && item.if_tongyi==0">
						<view>
							<tn-tag padding="0rpx 0rpx" width="80rpx" size="sm" shape="circle" backgroundColor="#28B93D"
								fontColor="#ffffff">已同意</tn-tag>
						</view>
					</block>
				</view>
			</view>
		</view>
		<view style="margin-top: 40rpx;">
			<tn-load-more :status="loadingIn" :loadText="{nomore:'没有消息'}"></tn-load-more>
		</view>
		<view class="tn-tabbar-height"></view>
		<tn-modal v-model="showMode.show" :title="showMode.title" :content="showMode.content" :button="showMode.button"
			@click="modeClick"></tn-modal>
	</view>
</template>

<script>
	import {
		MaillogIndex,
		MaillogUpdate,
		MaillogUpdateState
	} from "@/util/api";
	import store from "@/store";
	export default {
		name: 'Message',
		data() {
			return {
				loadingIn: 'loading',
				uid: 0,
				page: 1,
				size: 10,
				list: [],
				id: 0,
				if_tongyi: 0,
				showMode: {
					content: '',
					title: '',
					show: false,
					key: 1,
					button: [{
							text: '取消',
							backgroundColor: '#E6E6E6',
							fontColor: '#FFFFFF',
						},
						{
							text: '确定',
							backgroundColor: 'tn-bg-indigo',
							fontColor: '#FFFFFF'
						}
					]
				}

			}
		},
		onLoad() {
			var uid = uni.getStorageSync('uid');
			this.uid = uid;
			this.getMaillogIndex();
		},
		onReachBottom() {
			this.page = this.page + 1;
			this.getMaillogIndex();
		},
		methods: {
			all_upadte_do() {
				MaillogUpdateState({
						member_b_id: this.uid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							store.state.msgCount = 0;
							uni.showToast({
								title: '操作成功！',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
						this.showMode.show = false;
						this.page = 1;
						this.list = [];
						this.getMaillogIndex();
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			all_upadte() {
				this.showMode.show = true;
				this.showMode.content = '确定修改为全部已读吗？';
				this.showMode.title = '操作提示';
				this.showMode.key = 2;
			},
			updateType(type, id) {
				this.showMode.show = true;
				this.showMode.content = type == 0 ? '确定要通过申请吗？' : '确定要拒绝申请吗？';
				this.showMode.title = '操作提示';
				this.showMode.key = 1;
				this.id = id;
				this.if_tongyi = type;
			},
			setMaillogUpdate() {
				MaillogUpdate({
						id: this.id,
						if_tongyi: this.if_tongyi,
						state: 1,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '操作成功！',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
						this.showMode.show = false;
						this.page = 1;
						this.list = [];
						this.getMaillogIndex();
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getMaillogIndex() {
				MaillogIndex({
						member_b_id: this.uid,
						page: this.page,
						size: this.size,

					})
					.then(res => {
						console.log(res.data.length);
						this.list.push(...res.data);
						if (res.data.length == 0 || this.list.length < 5) {
							this.loadingIn = 'nomore';
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			modeClick(i) {
				if (i.index == 0) {
					this.showMode.show = false;
				}
				if (this.showMode.key == 1 && i.index == 1) {
					this.setMaillogUpdate();
				}
				if (this.showMode.key == 2 && i.index == 1) {
					this.all_upadte_do();
				}
			},
			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.message {
		max-height: 100vh;
	}

	/* 自定义导航栏内容 end */

	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 20rpx;
		height: calc(40rpx + env(safe-area-inset-bottom) / 2);
		height: calc(40rpx + constant(safe-area-inset-bottom));
	}


	.tn-color-wallpaper {
		color: #1D2541;
	}

	/* 页面阴影 start*/
	.wallpaper-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}



	/* 图标容器15 start */
	.icon15 {
		&__item {
			width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 105rpx;
				height: 105rpx;
				font-size: 60rpx;
				border-radius: 50%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;


				}
			}
		}
	}

	/* 用户头像 start */
	.logo-image {
		width: 100rpx;
		height: 100rpx;
		position: relative;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border: 1rpx solid rgba(255, 255, 255, 0.05);
		box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.05);
		border-radius: 50%;
		overflow: hidden;
		// background-color: #FFFFFF;
	}
</style>