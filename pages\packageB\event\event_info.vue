<template>
	<view style="letter-spacing: 1rpx;" v-cloak>
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between">
				<view class="tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="goBack()">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:'活动详情'}]" :current="topCurrent" activeColor="#000" :bold="false"
							:fontSize="36"></tn-tabs>
					</view>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<image :src="apiImgUrl+info.activity_image" style="width: 100%;" mode="widthFix"></image>
			<view>
				<view style="padding:20rpx 30rpx;">
					<view style="font-size: 34rpx;font-weight: 600;">{{ info.activity_name }}</view>
					<view style="margin-top: 40rpx;">
						<view>
							<text style="color: #26BB71;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">活动开始时间：</text>
							<text style="color: #979797;">{{ info.activity_start_time }}</text>
						</view>
						<view style="margin: 15rpx 0rpx;">
							<text style="color: #BE7E28;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">活动结束时间：</text>
							<text style="color: #979797;">{{ info.activity_end_time }}</text>
						</view>
						<view>
							<text style="color: #DB5022;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">活动地点：</text>
							<text style="color: #979797;">{{ info.activity_location }}</text>
						</view>
						<view style="margin: 15rpx 0rpx;">
							<text style="color: #0000FF;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">活动规模：</text>
							<text style="color: #979797;">{{info.number==0?'不限制':info.number+'人'}}</text>
						</view>
						<view v-if="info.activity_type!=1&&info.activity_type!=4">
							<view style="margin: 15rpx 0rpx;" v-if="info.if_display_registrants==2">
								<text style="color: #0000FF;">●</text>
								<text v-if="info.activity_type==3"
									style="margin-left: 10rpx;font-weight: 600;">捐赠人数：</text>
								<text v-if="info.activity_type==2"
									style="margin-left: 10rpx;font-weight: 600;">填写人数：</text>
								<text v-if="info.activity_type==1||info.activity_type==4"
									style="margin-left: 10rpx;font-weight: 600;">报名人数：</text>
								<text style="color: #979797;">{{ info.list.length}}人</text>
							</view>
							<view style="margin: 15rpx 0rpx;" v-if="info.if_display_registrants==1"
								class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between"
								@click="rightMod = true">
								<view class="tn-flex tn-flex-center tn-flex-col-center">
									<view style="color: #0000FF;">●</view>
									<view v-if="info.activity_type==3" style="margin-left: 10rpx;font-weight: 600;">
										捐赠详情：</view>
									<view v-if="info.activity_type==2" style="margin-left: 10rpx;font-weight: 600;">
										填写详情：</view>
									<view v-if="info.activity_type==1||info.activity_type==4"
										style="margin-left: 10rpx;font-weight: 600;">报名详情：</view>
									<tn-avatar-group :border="false" :lists="info.list.slice(0, 8)"></tn-avatar-group>
								</view>
								<view>
									<text style="margin-right: 10rpx">{{info.list.length}}人</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="info.activity_type==1||info.activity_type==4">
					<view style="width: 100%;height: 10rpx;background-color: #EBF4F7"></view>
					<view style="padding:20rpx 30rpx;">
						<view style="margin: 15rpx 0rpx;">
							<text style="color: #26BB71;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">报名开始时间：</text>
							<text style="color: #979797;">{{ info.signup_start_time }}</text>
						</view>
						<view style="margin: 15rpx 0rpx;">
							<text style="color: #BE7E28;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">报名结束时间：</text>
							<text style="color: #979797;">{{ info.signup_end_time }}</text>
						</view>
						<view style="margin: 15rpx 0rpx;" v-if="info.if_display_registrants==1">
							<text style="color: #0000FF;">●</text>
							<text style="margin-left: 10rpx;font-weight: 600;">报名人数：</text>
							<text style="color: #979797;">{{ info.list.length}}人</text>
						</view>
						<view style="margin: 15rpx 0rpx;" v-if="info.if_display_registrants==1"
							class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between"
							@click="rightMod = true">
							<view class="tn-flex tn-flex-center tn-flex-col-center">
								<view style="color: #0000FF;">●</view>
								<view style="margin-left: 10rpx;font-weight: 600;">报名详情：</view>
								<tn-avatar-group :border="false" :lists="info.list.slice(0, 8)"></tn-avatar-group>
							</view>
							<view>
								<text style="margin-right: 10rpx">{{info.list.length}}人</text>
								<text class="tn-icon-right"></text>
							</view>
						</view>
					</view>
				</view>
				<view style="width: 100%;height: 10rpx;background-color: #EBF4F7"></view>
				<view style="padding:20rpx 30rpx;">
					<view style="margin-top: 20rpx;font-size: 32rpx;font-weight: 600;">活动介绍</view>
					<view>
						<view class="ks_html" style="line-height: 50rpx;margin-top: 20rpx;">
							<view v-if="info.video!=null&&info.video!=''">
								<video :src="apiImgUrl+info.video" style="width: 100%;border-radius: 5rpx;"></video>
							</view>
							<view v-html="info.activity_content"></view>
						</view>
					</view>
					<view v-if="info.activity_type==1||info.activity_type==3" style="height: 180rpx;"></view>
					<view v-if="info.activity_type==2" style="padding-bottom: 180rpx;">
						<view style="margin-top: 60rpx;font-size: 32rpx;font-weight: 600;">问卷问题</view>
						<tn-form labelPosition="top" :model="form" ref="form">
							<template v-for="(item,index) in questionnaireList">
								<tn-form-item v-if="item.type==2" :label="(index+1)+'、'+item.title+'（多选题）'"
									prop="hobby">
									<tn-checkbox-group v-model="questionnaireList[index].topic" size="40"
										activeColor="#82B2FF" wrap>
										<tn-checkbox :name="op.name" v-for="(op,opi) in item.option">
											{{ op.name }}、{{ op.val }}
										</tn-checkbox>
									</tn-checkbox-group>
								</tn-form-item>
								<tn-form-item v-if="item.type==1" :label="(index+1)+'、'+item.title+'（单选题）'"
									prop="danxuan">
									<tn-radio-group v-model="questionnaireList[index].topic" size="40"
										activeColor="#82B2FF" wrap>
										<tn-radio :name="op.name" v-for="(op,opi) in item.option">
											{{ op.name }}、{{ op.val }}
										</tn-radio>
									</tn-radio-group>
								</tn-form-item>
								<tn-form-item v-if="item.type==3" :label="(index+1)+'、'+item.title+'（问答题）'"
									prop="wenda">
									<tn-input placeholder="请填写答案" v-model="questionnaireList[index].topic"
										type="textarea" />
								</tn-form-item>
							</template>
						</tn-form>
					</view>
				</view>
			</view>
		</view>
		<view style="position: fixed;bottom: 30rpx; width: 100%;">
			<!--			<view class="tn-flex tn-flex-center tn-flex-row-center tn-flex-col-center"-->
			<!--				style="background-color: #FDF2EC;text-align: center;padding: 30rpx;">-->
			<!--				<view style="font-weight: 600;">-->
			<!--					<text style="margin-right: 10rpx;">距报名结束剩<text style="color: #F73909;">119</text>天</text>-->
			<!--					<tn-count-down fontColor="#F73909" backgroundColor="#FDF2EC" :fontSize="30" :timestamp="86400"-->
			<!--						:showDays="true" :showHours="true" :showSeconds="true" :showMinutes="true"></tn-count-down>-->
			<!--				</view>-->
			<!--			</view>-->
			<view style="text-align: center;padding:0 30rpx;" v-if="info.activity_type==1">
				<button class="mmy" hover-class="none" @click="application_add" v-if="info.type=='进行中'">确认报名</button>
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='已结束'">报名已结束</button>
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='未开始'">报名未开始</button>
			</view>
			<view style="text-align: center;padding:0 30rpx;" v-if="info.activity_type==2">
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='未开始'">问卷未开始</button>
				<button class="mmy" hover-class="none" @click="option_add" v-if="info.type=='进行中'">确认提交</button>
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='已结束'">问卷已结束</button>
			</view>
			<view style="text-align: center;padding:0 30rpx;" v-if="info.activity_type==3">
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='未开始'">捐助未开始</button>
				<button class="mmy" hover-class="none" @click="juanMod=true" v-if="info.type=='进行中'">我要捐助</button>
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='已结束'">捐助已结束</button>
			</view>
			<view style="text-align: center;padding:0 30rpx;" v-if="info.activity_type==4">
				<button class="mmy" hover-class="none" @click="peiMod=true" v-if="info.type=='进行中'">我要参加</button>
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='已结束'">报名已结束</button>
				<button class="mmy" hover-class="none" :disabled="true" v-if="info.type=='未开始'">报名未开始</button>
			</view>
		</view>
		<tn-modal v-model="juanMod" :custom="true">
			<view style="padding: 20rpx;text-align: center;font-size: 40rpx;">感谢您的捐助</view>
			<tn-form :labelWidth="180">
				<tn-form-item label="捐助金额">
					<tn-input v-model="money" type="decimal" placeholder="请填写捐助金额" />
				</tn-form-item>
			</tn-form>
			<view class="tn-flex tn-flex-row-center" style="margin-top: 50rpx;">
				<tn-button @click="juanMod=false" backgroundColor="#E6E6E6" fontColor="#838383">我再想想</tn-button>
				<tn-button @click="juanzhuDo" backgroundColor="#28B93D" fontColor="#ffffff"
					style="margin-left: 70rpx;">确定捐助
				</tn-button>
			</view>
		</tn-modal>
		<tn-modal v-model="peiMod" :custom="true">
			<view style="padding: 20rpx;text-align: center;font-size: 40rpx;">需要缴纳费用</view>
			<view style="text-align: center;margin-top: 30rpx;">
				<text style="font-size: 40rpx;">¥</text>
				<text style="font-size: 70rpx;color: #26BB71;margin-left: 5rpx;font-weight: 600;">{{info.money}}</text>
			</view>
			<view class="tn-flex tn-flex-row-center" style="margin-top: 50rpx;">
				<tn-button @click="peiMod=false" backgroundColor="#E6E6E6" fontColor="#838383">我再想想</tn-button>
				<tn-button @click="zhifuDo" backgroundColor="#28B93D" fontColor="#ffffff"
					style="margin-left: 70rpx;">支付费用
				</tn-button>
			</view>
		</tn-modal>
		<tn-popup v-model="rightMod" mode="right" width="70%">
			<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="rightMod = false">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:'报名详情'}]" :current="topCurrent" activeColor="#000" :bold="false"
							:fontSize="24"></tn-tabs>
					</view>
				</view>
			</tn-nav-bar>
			<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
				<view v-if="info.list.length!=0" class="tn-flex tn-flex-center tn-flex-col-center"
					v-for="(item,index) in info.list" @click="openUrl('/pages/packageA/user/my_card?id='+item.id)"
					style="margin-top: 20rpx;padding: 20rpx;">
					<view>
						<image :src="apiImgUrl+item.photo_image"
							style="width: 100rpx;height: 100rpx;border-radius: 50%"></image>
					</view>
					<view style="margin-left: 20rpx;">
						<view style="font-size: 30rpx;font-weight: 600">{{item.nikename}}</view>
						<view style="font-size: 24rpx;color: #999999;margin-top: 10rpx">{{item.position_name}}</view>
					</view>
				</view>
				<view v-if="info.list.length==0" style="text-align: center;padding-top: 40rpx">暂无</view>
			</view>
		</tn-popup>
		<tn-modal v-model="showLogin" :custom="true">
			<view style="text-align: center;">
				<view style="font-size: 32rpx;font-weight: 600;">登陆提示</view>
				<view style="margin-top: 40rpx;">请登录后参与活动！</view>
			</view>
			<view style="text-align: center;margin-top: 40rpx;">
				<tn-button open-type="getPhoneNumber" @getphonenumber="PhoneLogin" backgroundColor="#3377FF"
					height="75rpx" width="500rpx" shape="round" fontColor="#ffffff">点击登陆</tn-button>
			</view>
		</tn-modal>
	</view>
</template>

<script>
	import {
		eventInfo,
		questionnaireFind,
		applicationAdd,
		questionnaireAdd,
		moneyLogAdd,
		wxphoneLogin
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		data() {
			return {
				showLogin: false,
				money: '',
				juanMod: false,
				rightMod: false,
				peiMod: false, //培训付费
				questionnaireList: [], //问卷列表
				button: [{
						text: '取消',
						backgroundColor: '#E6E6E6',
						fontColor: '#FFFFFF',
						shape: 'round'
					},
					{
						text: '确定',
						backgroundColor: 'tn-bg-indigo',
						fontColor: '#FFFFFF'
					}
				],
				form: {
					wenda: '',
					danxuan: '',
					hobby: [],
				},
				topCurrent: 0,
				id: 0,
				info: {
					list: []
				},
				apiImgUrl: this.$store.state.imgUrl
			}
		},
		mounted() {
			// this.$wxshare({
			// 	url: 'http://ysh.0rui.cn/#/pages/index/event_info',
			// 	data: {
			// 		url: window.location.href.split("#")[0],
			// 	},
			// 	share_data: {
			// 		title: '河南省青年企业家协会开展走进开封市龙亭区暨产业考察交流活动',
			// 		desc: '河南省青年企业家协会开展走进开封市龙亭区暨产业考察交流活动',
			// 		imgUrl: 'http://ysh.0rui.cn/static/ser.png',
			// 		link: "http://ysh.0rui.cn/#/pages/index/event_info", // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
			// 	}
			// })
			// var id =  this.$route.query.id;
			// this.id = id;
			// this.getEventInfo();
		},
		onShareAppMessage() { // 分享到微信好友
			// 更多参数配置，参考文档
			return {
				title: this.info.activity_name,
				path: '/pages/packageB/event/event_info?id=' + this.info.id + "&gid=" + store.state.Gid,
				imageUrl: this.apiImgUrl + this.info.activity_image,
			}
		},
		onShareTimeline() { // 分享到朋友圈
			return {
				title: this.info.activity_name,
				path: '/pages/packageB/event/event_info?id=' + this.info.id + "&gid=" + store.state.Gid,
				imageUrl: this.apiImgUrl + this.info.activity_image,
			}
		},

		onLoad(d) {
			var uid = uni.getStorageSync('uid');
			console.log(d);
			this.id = d.id;
			if (typeof(d.gid) != 'undefined') {
				console.log(11111111);
				store.commit('$tStore', {
					name: 'Gid',
					value: d.gid
				})
				uni.setStorageSync('Gid', d.gid);
			}
			if (!uid) {
				getApp().getUserLogin((r) => {
					console.log('---Login---', r);
				})
			}
			this.getEventInfo();
		},
		methods: {
			PhoneLogin(d) {
				console.log(d);
				var openid = uni.getStorageSync('openid');
				wxphoneLogin({
						code: d.code,
						openid: openid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '登陆成功！',
								icon: 'none',
								duration: 2000
							});
							uni.setStorageSync('uid', res.data.id);
							uni.setStorageSync('userInfo', res.data);
							this.userInfo = res.data;
							this.showLogin = false;
						} else {
							uni.showToast({
								title: '登陆失败！',
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			zhifuDo() {
				this.peiMod = false;
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					this.showLogin = true;
					return;
				}
				applicationAdd({
						association_id: store.state.Gid,
						member_id: uid,
						activity_id: this.id,
					})
					.then(res => {
						if (res.code == 1) {
							uni.showToast({
								title: '报名成功！',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})

			},
			juanzhuDo() {
				this.juanMod = false;
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					this.showLogin = true;
					return;
				}
				moneyLogAdd({
						association_id: store.state.Gid,
						member_id: uid,
						activity_id: this.id,
						money: this.money
					})
					.then(res => {
						if (res.code == 1) {
							uni.showToast({
								title: '捐助成功！',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			option_add() {
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					this.showLogin = true;
					return;
				}
				var that = this;
				var uid = uni.getStorageSync('uid');
				var key = this.questionnaireList.map((item, index) => {
					if (!('topic' in item)) {
						return '';
					} else {
						return {
							activity_id: that.id,
							topic_id: item.id,
							member_id: uid,
							option: Array.isArray(item.topic) ? item.topic.join(',') : item.topic,
						}
					}
				});
				for (var i = 0; i < key.length; i++) {
					if (key[i] == '') {
						uni.showToast({
							title: '请填写第[' + (i + 1) + ']个问题',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}
				questionnaireAdd({
						option: JSON.stringify(key),
						activity_id: this.id,
						member_id: uid,
						association_id: store.state.Gid,
					})
					.then(res => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			application_add() {
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					this.showLogin = true;
					return;
				}
				applicationAdd({
						association_id: store.state.Gid,
						member_id: uid,
						activity_id: this.id,
						//application_time: new Date().toISOString().slice(0, 19).replace('T', ' ')
					})
					.then(res => {
						if (res.code == 1) {
							uni.showToast({
								title: '报名成功！',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getEventInfo() {
				eventInfo({
						association_id: store.state.Gid,
						id: this.id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							if (key.activity_content.indexOf("<img") != -1) {
								key.activity_content = getApp().addWidthToImages(key.activity_content);
							}
							this.info = key;
							if (this.info.activity_type == 2) {
								this.getEventOption()
							}
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getEventOption() {
				questionnaireFind({
						activity_id: this.id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.questionnaireList = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		},
	}
</script>

<style>
	.mmy {
		background-color: #F73909;
		border-radius: 30px;
		height: 80rpx;
		width: 100%;
		color: #FFF;
		line-height: 80rpx;
		font-size: 28rpx;
	}
</style>