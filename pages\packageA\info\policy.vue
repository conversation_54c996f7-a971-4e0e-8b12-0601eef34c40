<template>
	<view>
		<!-- <tn-nav-bar customBack :bottomShadow="false" backgroundColor="#ffffff">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
				<view style="color: #000000;;text-align: left;font-size: 36rpx;font-weight: 600;">
					<text>惠企政策</text>
				</view>
			</view>
			<view slot="back" class='tn-custom-nav-bar__back' style="padding-left: 20rpx;" @click="goBack">
				<image src="/static/h_back.png" style="width: 60rpx;height: 60rpx;"></image>
			</view>
		</tn-nav-bar> -->
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="false" backgroundColor="#ffffff">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top">
				<tn-tabs :list="[{name:'惠企政策'}]" :current="topCurrent" activeColor="#000" :bold="false"
					:fontSize="36"></tn-tabs>
				</view>
			</view> 
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}" style="padding-bottom: 60rpx;">
			<tn-list-view :card="true" unlined="all">
				<block v-for="(item, index) in list" :key="index">
					<tn-list-cell padding="36rpx" :arrow="true"
						@click="openUrl('/pages/packageA/info/policy_list?id='+item.id)">
						<image :src="apiImgUrl+item.image"
							style="width: 50rpx;height: 50rpx;vertical-align: middle;margin-right: 20rpx;"></image>
						{{item.name}}
						<view class="list__right">
							<view class="tn-text-sm tn-color-gray">{{item.category_count}}</view>
						</view>
					</tn-list-cell>
				</block>
			</tn-list-view>
		</view>
	</view>
</template>

<script>
	import {
		getArticlePolicy
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				apiImgUrl: store.state.imgUrl,
				list: []
			}
		},
		onLoad() {
			this.getArticlePolicyList();
		},
		methods: {
			getArticlePolicyList() {
				getArticlePolicy()
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list = res.data;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style scoped lang="scss">

  .list {
    &__right {
     position: absolute;
	 right: 68rpx;
	 top: 48rpx;
    }
  }
</style>