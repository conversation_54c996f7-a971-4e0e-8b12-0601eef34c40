<template>
	<view class="message tn-safe-area-inset-bottom">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'会员卡核销'}]" activeColor="#000" :bold="false" :fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view class="" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view v-if="show">
				<view style="text-align: center;padding: 30rpx;font-size: 50rpx;font-weight: 600">会员卡核销</view>
				<view style="font-size: 30rpx;font-weight: 600;padding:20rpx 30rpx 20rpx 30rpx">持卡人信息：</view>
				<view style="padding:20rpx 30rpx 0rpx 30rpx">会员卡号：{{code}}</view>
				<view class="tn-flex tn-flex-col-center tn-flex-row-between"
					style="background-color: #ffffff;padding:50rpx 30rpx;border-radius: 15rpx;margin-bottom: 20rpx;">
					<view class="tn-flex tn-flex-col-center">
						<view>
							<image :src="apiImgUrl+info.photo_image"
								style="width: 100rpx;height: 100rpx;border-radius: 50%;">
							</image>
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 30rpx;font-weight: 600;">{{info.member_nikename}}</view>
							<view style="font-size: 28rpx;margin-top: 10rpx">{{info.member_phone}}</view>
						</view>
					</view>
				</view>
				<view style="font-size: 30rpx;font-weight: 600;padding:20rpx 30rpx 20rpx 30rpx">核销人：{{info.clerk_name}}
				</view>
				<view style="margin-top: 100rpx;text-align: center">
					<tn-button @click="submitDo" width="400rpx" height="80rpx" backgroundColor="#28B93D"
						fontColor="#ffffff" shape="round">确认核销</tn-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		ifRightToExamine,
		addWriteoff
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				show: false,
				code: '',
				apiImgUrl: this.$store.state.imgUrl,
				info: {},
        key:'',
			}
		},
		onLoad(query) {
			const q = decodeURIComponent(query.q) // 获取到二维码原始链接内容
			var data = this.getUrlParams(q);
			this.code = data.key;
      this.key = data.random_number;
			this.association_id = data.association_id;
			getApp().getUserLogin((r) => {
				console.log('111', r);
				this.phone = r.data.phone;
				this.checkUser();
			})
		},
		methods: {
			submitDo() {
				addWriteoff({
						association_id: this.association_id,
						phone: this.phone,
						code_number: this.code,
          random_number:this.key
					})
					.then(res => {
						console.log(res);
            if(res.code==1){
              uni.showToast({
                title: '核销成功！',
                icon: 'none',
                duration: 2000
              });
            }else{
              uni.showModal({
                title: '提示',
                content: res.msg,
                showCancel: false,
                success: function(res) {
                  that.goBack();
                }
              });
            }
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			checkUser() {
				var that = this;
				console.log(this.association_id);
				console.log(this.phone);
				console.log(this.code);
				ifRightToExamine({
						association_id: this.association_id,
						phone: this.phone,
						code_number: this.code,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.show = true;
							this.info = res.data;
						} else {
							this.show = false;
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: function(res) {
									that.goBack();
								}
							});

						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getUrlParams(url) {
				// \w+ 表示匹配至少一个(数字、字母及下划线), [\u4e00-\u9fa5]+ 表示匹配至少一个中文字符
				let pattern = /(\w+|[\u4e00-\u9fa5]+)=(\w+|[\u4e00-\u9fa5]+)/ig;
				let result = {};
				url.replace(pattern, ($, $1, $2) => {
					result[$1] = $2;
				})
				return result
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>

</style>
