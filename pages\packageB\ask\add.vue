<template>
	<view style="background-color: #F5F5F5;height: 100vh;">
		<view class="ask_add_bg">
			<tn-nav-bar customBack :bottomShadow="false" backgroundColor="transparent">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
					<view style="color: #000000;;text-align: left;font-size: 36rpx;font-weight: 600;">
						<text></text>
					</view>
				</view>
				<view slot="back" class='tn-custom-nav-bar__back' style="padding-left: 20rpx;" @click="goBack">
					<image src="/static/w_back.png" style="width: 60rpx;height: 60rpx;"></image>
				</view>
			</tn-nav-bar>
			<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
				<view style="padding: 30rpx;margin-top: 200rpx;">
					<view style="background-color: #ffffff;border-radius: 18rpx;padding: 30rpx;">
						<tn-input placeholder="请输入您的疑问" v-model="issue_names" type="textarea" :clearable="false"
							:border="false" :height="400" :autoHeight="false"></tn-input>
						<view style="min-height: 200rpx;">
							<tn-image-upload-drag ref="imageUpload" :action="action" :width="200" :height="200"
								:formData="formData" :fileList="fileList" :autoUpload="true" :maxCount="3"
								:showUploadList="true" :showProgress="false" name="file" :deleteable="true"
								:customBtn="false" @sort-list="onSortList" @on-list-change="onListChange"
								:beforeUpload="beforeUpload" uploadText="添加图片" />
						</view>
					</view>
				</view>
			</view>

		</view>
		<view style="position: fixed;bottom: 100rpx;width: 100%;">
			<view @click="onSubmit"
				style="text-align: center;font-size: 32rpx;color: #ffffff;width: 90%;margin: 0 auto;background-color:#2368F2;line-height: 85rpx;height: 85rpx;border-radius: 85rpx;">
				提交问题</view>
		</view>
	</view>
</template>

<script>
	import {
		getNwwbAdd
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				action: store.state.apiUrl+'/common/upload',
				//action: 'http://**************/api/common/upload',
				issue_names: '',
				fileList: '',
				formData: {},
				submitLoging: false,
			}
		},
		methods: {
			onSubmit() {
				if (this.submitLoging) {
					return;
				}
				this.submitLoging = true;
				var user = uni.getStorageSync('userInfo');
				let problem_images = [];
				this.fileList.forEach(function(item, index) {
					problem_images.push(item.response.data.url);
				});
				var form = {};
				form.association_id = user.association_id;
				form.issue_names = this.issue_names;
				form.problem_images = problem_images.join(",");
				getNwwbAdd(form)
					.then(res => {
						console.log(res);
						if(res.code==1){
							uni.showToast({
								title: '添加成功！',
								icon: 'none',
								duration: 2000
							});
							setTimeout(()=>{
								uni.redirectTo({
									url:'/pages/packageB/ask/user_list'
								})
							},1500)
						}else{
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			onListChange(list) {
				console.log(list);
				this.fileList = list;
			},
			beforeUpload(e, list) {
				console.log(e);
			},
			// 图片拖拽重新排序
			onSortList(list) {
				console.log(list);
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style scoped>
	.ask_add_bg {
		/* background: url('https://ysx.hschool.com.cn/uploads/1/20250509/94f9329dab451d4fc9f6b409d96c2a13.png') no-repeat; */
		background: url('https://ysx.0rui.cn/uploads/1/20250509/94f9329dab451d4fc9f6b409d96c2a13.png') no-repeat;
		
		background-size: 100%;
		height: 100%;
	}
</style>