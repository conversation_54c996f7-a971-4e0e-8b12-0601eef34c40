<template>
	<view style="background-color: #F7F7F7;">
		<image src="/static/new_img/home_bg.png" style="width: 100%;height: 435rpx;"></image>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between" style="padding-top: 30rpx;">
				<view class="tn-color-gray--dark"
					style="width: 100%;margin: 0rpx 30rpx 0 30rpx;border-radius: 100rpx;padding-left: 6rpx;background-color: #ffffff;"
					@click="openUrl('/pages/index/search')">
					<tn-notice-bar :list="searlist" mode="vertical" leftIconName="search"
						:duration="6000"></tn-notice-bar>
				</view>
				<view class="tn-flex" style="margin:0px 30rpx 0rpx 0rpx;">
					<tn-button @click="openUrl('/pages/index/search')" backgroundColor="#ffffff" fontColor="#666666"
						shape="round" width="140rpx" height="70rpx">搜索
					</tn-button>

					<!--					<image @click="openUrl('/pages/index/my_assist')" src="/static/t1.png"-->
					<!--						style="width: 35rpx;height: 35rpx;"></image>-->
					<!--					<view style="position: relative;">-->
					<!--						<image @click="openUrl('/pages/index/my_msg')" src="/static/t2.png"-->
					<!--							style="width: 35rpx;height: 35rpx;margin-left: 40rpx;"></image>-->
					<!--						<tn-badge v-if="msg()>0" style="position: absolute;top: -20rpx;right: -20rpx;"-->
					<!--							backgroundColor="#E83A30" fontColor="#ffffff">{{msg()}}</tn-badge>-->
					<!--					</view>-->
				</view>
			</view>

			<view style="margin-top: 20rpx;">
				<swiper class="card-swiper" style="height: 365rpx" current="0" mode="dot" :circular="true"
					duration="500" interval="5000" @change="cardSwiper" :autoplay="true">
					<swiper-item style="padding: 0px 28rpx;height: 365rpx;border-radius: 15rpx;"
						v-for="(item,index) in carousel_list" :key="index" :class="cardCur==index?'cur':''">
						<video :muted="true" v-if="item.type==2" id="myVideo" :src="apiImgUrl+item.image"
							:controls="false" loop autoplay object-fit="contain"
							style="width: 100%;border-radius: 15rpx;height: 100%;" @error="videoErrorCallback"></video>
						<image v-if="item.type==1" :src="apiImgUrl+item.image" mode="aspectFit"
							style="width: 100%;height: 365rpx;border-radius: 15rpx;">
						</image>
					</swiper-item>
				</swiper>
			</view>
			<view class="indication">
				<block v-for="(item,index) in carousel_list" :key="index">
					<view class="spot" :class="cardCur==index?'active':''"></view>
				</block>
			</view>
		</view>
		<view style="padding: 0px 30rpx;margin-bottom: 20rpx;">
			<view style="background-color: #FFF;border-radius: 16rpx;">
				<cc-noticeBar :noticeList="noticeList" colors="#000000" @click="itemClick"></cc-noticeBar>
			</view>
		</view>
		<view style="background-color: #FFF;border-radius: 18rpx;margin: 25rpx;">
			<tn-grid align="center" :col="4">
				<tn-grid-item style="width: 25%" @click="openUrl('/pages/index/pizz_info?id='+association_id)">
					<view style="padding: 30rpx;text-align: center;">
						<image src="/static/h1.png" style="width: 100rpx;height: 100rpx;"></image>
						<view style="font-size: 28rpx;font-weight: normal;margin-top: 10rpx;">协会简介</view>
					</view>
				</tn-grid-item>
				<tn-grid-item style="width: 25%" @click="openUrl('/pages/index/news')">
					<view style="padding: 30rpx;text-align: center;">
						<image src="/static/h2.png" style="width: 100rpx;height: 100rpx;"></image>
						<!-- <view style="font-size: 28rpx;font-weight: normal;margin-top: 10rpx;">协会活动</view> -->
						<view style="font-size: 28rpx;font-weight: normal;margin-top: 10rpx;">协会新闻</view>
					</view>
				</tn-grid-item>
				<tn-grid-item style="width: 25%" @click="openUrl('/pages/packageA/info/joinUs')">
					<view style="padding: 32rpx 30rpx;text-align: center;">
						<image src="/static/joinus.png" style="width: 100rpx;height: 100rpx;"></image>
						<view style="font-size: 28rpx;font-weight: normal;margin-top: 10rpx;">加入我们</view>
					</view>
				</tn-grid-item>
				<tn-grid-item style="width: 25%" @click="openMiniProgramLink()">
					<view style="padding: 32rpx 30rpx;text-align: center;">
						<image src="/static/diverseYouth.png" style="width: 100rpx;height: 100rpx;"></image>
						<view style="font-size: 28rpx;font-weight: normal;margin-top: 12rpx;">多样青春</view>
					</view>
				</tn-grid-item>
				<!-- <button style="width: 25%;background-color: #ffffff;" @click="openMiniProgramLink">
					<view style="padding: 32rpx 30rpx;text-align: center;">
						<image src="/static/diverseYouth.png" style="width: 100rpx;height: 100rpx;position: relative;top: 34rpx;"></image>
						<view style="font-size: 28rpx;font-weight: normal;margin-top: 8rpx;">多样青春</view>
					</view>
				</button> -->
				
				<web-view 
				      v-if="showWebView" 
				      :src="miniProgramUrl" 
				      @message="handleWebViewMessage()"
				    ></web-view>
			</tn-grid>
		</view>
		<!-- <view class="tn-flex tn-flex-row-between tn-flex-col-center tn-flex-row-center" style="padding: 0px 28rpx;">
			<view @click="openUrl('/pages/index/pizz_info?id='+association_id)"
				style="position:relative;padding: 20rpx;border-radius: 16rpx;height: 270rpx;width: 100%;background: linear-gradient(275.57deg, rgba(193, 237, 217, 1) 1.39%, rgba(188, 237, 216, 1) 112.49%);">
				<view style="font-size: 32rpx">协会简介</view>
				<view style="font-size: 22rpx;margin-top: 10rpx">Association introduction</view>
				<image src="/static/48098165.png"
					style="width: 150rpx;height: 150rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
			</view>
			<view style="width: 100%;margin-left: 20rpx">
				<view @click="openUrl('/pages/index/service?type='+1)"
					style=" position:relative;padding: 20rpx;border-radius: 16rpx;height: 130rpx;background: linear-gradient(96.63deg, rgba(189, 224, 249, 1) 11.78%, rgba(205, 233, 251, 1) 103.76%);">
					<view style="font-size: 32rpx">协会活动</view>
					<view style="font-size: 22rpx;margin-top: 10rpx">Offline events</view>
					<image src="/static/48098164.png"
						style="width: 90rpx;height: 90rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
				</view>
				<view @click="openUrl('/pages/index/service?type='+3)"
					style="position:relative;margin-top: 10rpx;padding: 20rpx;border-radius: 16rpx;height: 130rpx;background: linear-gradient(279.38deg, rgba(246, 217, 197, 1) -5.05%, rgba(245, 209, 189, 1) 121.63%);">
					<view style="font-size: 32rpx">公益捐赠</view>
					<view style="font-size: 22rpx;margin-top: 10rpx">Public donations</view>
					<image src="/static/48098162.png"
						style="width: 90rpx;height: 90rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
				</view>
			</view>
		</view> -->
		<!-- <view class="tn-flex tn-flex-row-between tn-flex-col-center tn-flex-row-center"
			style="padding: 0px 28rpx;margin-top: 10rpx">
			<view @click="openUrl('/pages/index/service?type='+2)"
				style="position:relative;padding: 20rpx;border-radius: 16rpx;height: 130rpx;width: 100%;background: linear-gradient(280.64deg, rgba(251, 236, 198, 1) 0%, rgba(248, 225, 183, 1) 117.05%);">
				<view style="font-size: 32rpx">调查问卷</view>
				<view style="font-size: 22rpx;margin-top: 10rpx">Questionnaires</view>
				<image src="/static/48098160.png"
					style="width: 90rpx;height: 90rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
			</view>
			<view style="width: 100%;margin-left: 20rpx">
				<view @click="openUrl('/pages/index/service?type='+4)"
					style="position:relative;padding: 20rpx;border-radius: 16rpx;height: 130rpx;background: linear-gradient(96.63deg, rgba(226, 228, 245, 1) 11.78%, rgba(234, 236, 255, 1) 103.76%);">
					<view style="font-size: 32rpx">学习培训</view>
					<view style="font-size: 22rpx;margin-top: 10rpx">Learn and train</view>
					<image src="/static/48098163.png"
						style="width: 90rpx;height: 90rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
				</view>
			</view>
		</view> -->
		<!-- <view class="tn-flex tn-flex-row-between tn-flex-col-center tn-flex-row-center"
			style="padding: 0px 28rpx;margin-top: 10rpx">
			<view @click="openUrl('/pages/packageA/info/policy')"
				style="position:relative;padding: 20rpx;border-radius: 16rpx;height: 130rpx;width: 100%;background: linear-gradient(90deg, #d6eafa 0%, #d4eafe 100%);">
				<view style="font-size: 32rpx">惠企政策</view>
				<view style="font-size: 22rpx;margin-top: 10rpx">Corporate policies</view>
				<image src="/static/3162033.png"
					style="width: 90rpx;height: 90rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
			</view>
			<view style="width: 100%;margin-left: 20rpx">
				<view @click="openApply()"
					style="position:relative;padding: 20rpx;border-radius: 16rpx;height: 130rpx;background: #FFEDD2;">
					<view style="font-size: 32rpx">申请入会</view>
					<view style="font-size: 22rpx;margin-top: 10rpx">Apply for membership</view>
					<image src="/static/1000006795.png"
						style="width: 90rpx;height: 90rpx;position: absolute; right: 10rpx;bottom: 0;"></image>
				</view>
			</view>
		</view> -->


		<!-- <view style="width: 92%;background-color: #ffffff;border-radius: 16rpx;margin: 20rpx auto;overflow: hidden;"> -->
		<!-- <tn-list-cell>
				<view class="list-icon-text">
					<view class="list__left">
						<view class="list__left__icon">
							<image src="/static/hqzc.png" style="width: 56rpx;height: 50rpx;"></image>
						</view>
						<view class="list__left__icon"
							style="width: 4rpx;height: 42rpx;border-radius: 56rpx;background: #d1d4d180;"></view>
						<view class="list__left__text">惠企政策推送平台</view>
					</view>
					<view class="list__right">
						<text class="tn-icon-right tn-margin-right-xs"></text>
					</view>
				</view>
			</tn-list-cell> -->
		<!-- <tn-notice-bar :list="list" @click="openInfo" leftIconType="img" mode="vertical" :rightIcon="true"
				backgroundColor="#ffffff" :radius="16" @clickRight="openRight" @clickLeft="openRight"></tn-notice-bar> -->
		<!-- </view> -->

		<!--    <view class="tn-flex tn-flex-row-between tn-flex-col-center tn-flex-row-center"-->
		<!--          style="padding:0px 30rpx 0rpx 30rpx;text-align: center;font-size: 28rpx;">-->
		<!--      <view @click="openUrl('/pages/index/tissue')"-->
		<!--            style="width: 33%;background: linear-gradient(270deg, #3CBAEA, #6BC7F0);;border-radius: 16rpx;padding:25rpx 20rpx;">-->
		<!--        <view>-->
		<!--          <image src="/static/c3.png" style="width: 35rpx;height: 35rpx;vertical-align: middle;">-->
		<!--          </image>-->
		<!--        </view>-->
		<!--        <view style="margin-top: 10rpx;">-->
		<!--          <text style="color: #FFF;">组织架构</text>-->
		<!--        </view>-->
		<!--      </view>-->
		<!--      <view @click="openUrl('/pages/index/service')"-->
		<!--            style="width: 33%;background: linear-gradient(270deg, #EE7E45, #EE9657);border-radius: 16rpx;padding:25rpx 20rpx;margin-left:10rpx;">-->
		<!--        <view>-->
		<!--          <image src="/static/c2.png" style="width: 35rpx;height: 35rpx;vertical-align: middle;">-->
		<!--          </image>-->
		<!--        </view>-->
		<!--        <view style="margin-top: 10rpx;">-->
		<!--          <text style="color: #FFF;">协会活动</text>-->
		<!--        </view>-->
		<!--      </view>-->
		<!--      <view @click="openUrl('/pages/index/directory')"-->
		<!--            style="width: 33%;background: linear-gradient(270deg, #45B335, #89C33D);border-radius: 16rpx;padding:25rpx 20rpx;margin-left:10rpx;">-->
		<!--        <view>-->
		<!--          <image src="/static/c1.png" style="width: 35rpx;height: 35rpx;vertical-align: middle;">-->
		<!--          </image>-->
		<!--        </view>-->
		<!--        <view style="margin-top: 10rpx;">-->
		<!--          <text style="color: #FFF;">通讯录</text>-->
		<!--        </view>-->

		<!--      </view>-->
		<!--    </view>-->

		<view style="padding: 0rpx 28rpx 0rpx 28rpx;">
			<view style="background-color: #ffffff;border-radius:24rpx;width: 100%;padding-bottom: 15rpx;">
				<view @click.stop="openUrl('/pages/index/service')"
					class="tn-flex tn-flex-row-between tn-flex-col-center tn-flex-row-center"
					style="padding: 30rpx;width: 97%;">
					<!-- <view style="font-size: 32rpx;">协会活动</view> -->
					<view style="font-size: 32rpx;">协会动态</view>
					<!-- <view style="color: #808080;">
						<text>更多</text>
						<text class="tn-icon-right"></text>
					</view> -->
				</view>
				<scroll-view :scroll-x="true" style="padding:0rpx 30rpx;white-space: nowrap;width: 100%"
					v-if="actList.length>0">
					<!-- @click="openUrl('/pages/packageB/event/event_info?id='+item.id)" -->
					<view v-for="(item,index) in actList"
						
						@click="openNewUrl(item)"
						style="position: relative;;display: inline-block;width: 300rpx;text-align: center;background-color: #FFF;border-radius: 20rpx;overflow: hidden;margin-right: 20rpx;">
						<view>
							<image :src="apiImgUrl+item.news_image
" mode="aspectFill"
								style="width: 350rpx;height: 170rpx;">
							</image>
						</view>
						<view style="padding:10rpx;font-weight: 400;min-height: 100rpx;">
							<view class="tn-text-ellipsis-2" style="text-align: left;font-size: 28rpx">
								<text>{{ item.news_title }}</text>
							</view>
						</view>
						<!-- <view style="position: absolute;top: 0rpx;left: 0rpx;">
							<view v-if="item.type=='进行中'" class="my_tag" style="background-color: rgb(75, 130, 235)">
								进行中
							</view>
							<view v-if="item.type=='未开始'" class="my_tag" style="background-color: rgb(248, 155, 59)">
								预告
							</view>
							<view v-if="item.type=='已结束'" class="my_tag" style="background-color: rgb(225, 43, 51)">
								已结束
							</view>
						</view> -->
					</view>
				</scroll-view>
				<view style="background-color: #ffffff;padding: 20rpx;text-align: center" v-if="actList.length==0">
					暂无动态
				</view>
			</view>

		</view>
		<view style="padding:0rpx 28rpx;margin-top: 20rpx;">
			<view style="background-color: #ffffff;border-radius: 15rpx 15rpx 0px 0px;padding:20rpx;">
				<tn-tabs :list="goryList" :isScroll="true" :activeItemStyle="{fontSize:'30rpx',fontWeight:'600'}"
					activeColor="#3377FF" :current="current" name="name" @change="change" :fontSize="28"></tn-tabs>
			</view>
		</view>
		<view style="padding:0px 28rpx;padding-bottom: 20rpx;min-height:1000rpx">
			<view v-for="(item,index) in news_list" :class="{ 'rounded_corner': index === 0 }"
				style="background-color: #ffffff;">
				<view class="tn-flex" @click="openNewUrl(item)" style="padding: 20rpx;min-height: 200rpx;">
					<view v-if="item.news_image">
						<image :src="apiImgUrl+item.news_image" mode="aspectFill"
							style="width: 230rpx;height: 180rpx;border-radius: 8rpx;"></image>
					</view>
					<view style="position:relative;padding:0rpx 10rpx 10rpx 15rpx;margin-left: 10rpx;">
						<view class="tn-text-ellipsis-3" style="font-size: 28rpx;">{{ item.news_title }}</view>
						<view class="tn-flex tn-flex-row-between"
							style="font-size:24rpx;width: 95%;color: rgb(171, 171, 171);position: absolute; bottom: 0rpx; min-width: 380rpx;overflow: hidden">
							<view>{{ item.name }}</view>
							<view>{{ formatTime(item.showtime) }}</view>
							<view>
								<text class="tn-icon-eye" style="vertical-align: middle;"></text>
								<text style="vertical-align: middle;">{{ item.news_hits }}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-if="news_list.length-1>index"
					style="background: rgba(217, 217, 217, 0.5);width: 90%;margin: 0 auto;height: 2rpx;"></view>
			</view>
		</view>
		<view>
			<tn-select :safeAreaInsetBottom="true" v-model="selectShow" mode="single" :list="selectList"
				@confirm="confirm"></tn-select>
		</view>
		<!-- <view style="height: 1rpx;background-color: #E6E6E6;width: 90%;margin: 0 auto;margin-top: 30rpx;"></view> -->
		<view @click="callPhone"
			style="padding-top:70rpx ;text-align: center;padding-bottom: 160rpx;font-size: 24rpx;color: rgba(153,153,153,0.5);letter-spacing: 2rpx;">
			<view>v{{vuex_version}}</view>
			<view>技术支持：洛商协工作小组</view>
			<view>电话：15503791530</view>
		</view>
	</view>

</template>

<script>
	import {
		newsGoryList,
		activityList,
		newsList,
		associationIndex,
		carouselIndex,
		Mailcoent,
		getArticlePolicyList,
		getNoticeList
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		data() {
			return {
				list: [],
				selectShow: false,
				selectList: [],
				topCurrent: 0,
				searlist: [{
					article_title: '会员名称/协会名称/活动/新闻'
				}],
				noticeList: [],
				cardCur: 0,
				isAndroid: true,
				goryList: [],
				news_list: [],
				actList: [],
				carousel_list: [],
				current: 0,
				page: 1,
				size: 10,
				gory_id: 0,
				type: 1,
				HomeTitle: '',
				apiImgUrl: this.$store.state.imgUrl,
				association_id: this.$store.state.Gid,
				miniProgramUrl: 'https://wxaurl.cn/P5rAY9EBpPs', // 替换为你的短链
				showWebView: false,
			}
		},
		mounted() {
			getApp().getUserLogin((r) => {
				console.log('---Login---', r);
			})
			this.registerGetGidListener();
			this.getNewsGoryList();
			// this.getActivityList();
			this.getAssociationIndex();
			this.getCarouselIndex();
			this.getMsg();
			this.getNotice();
			this.getTopicList();
			//this.getArticlePolicyListAll();
		},
		beforeDestroy() {
			// 移除事件监听器
			this.unregisterGetGidListener();
		},
		methods: {
			openMiniProgramLink() {
				// this.showWebView = true; // 显示web-view，触发加载
				// console.log(':', this.showWebView);
				// setTimeout(() => {
				// 	this.showWebView = false; // 2秒后隐藏（避免页面残留）
				// 	}, 2000);
				console.log(':', this.showWebView);
				uni.navigateTo({
				        // url: '/pages/webview?url=https://your-h5-page.com/redirect?target=https://wxaurl.cn/P5rAY9EBpPs',
						 url: '/pages/webview',
				});
			},
			handleWebViewMessage(e) {
			      console.log('web-view消息:', e.detail);
			},
			// jumpToMiniProgram() {
			// 	// 判断运行环境（App或H5）
			// 	if (uni.getSystemInfoSync().platform === 'ios' || uni.getSystemInfoSync().platform === 'android') {
			// 		uni.navigateToMiniProgram({
			// 			appId: '目标小程序的appid', // 替换为你要跳转的小程序appid
			// 			path: 'pages/index/index',  // 目标小程序的页面路径（可选）
			// 	          success(res) {
			// 	            console.log('跳转成功', res);
			// 	          },
			// 	          fail(err) {
			// 	            console.log('跳转失败', err);
			// 	          }
			// 	        });
			// 	      } else {
			// 	        uni.showToast({
			// 	          title: '当前环境不支持跳转小程序',
			// 	          icon: 'none'
			// 	        });
			// 	      }
			// },
			getNotice() {
				getNoticeList({
						page: 1,
						size: 10
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.noticeList = res.data.ret;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			itemClick(item) {
				console.log(item);
				uni.navigateTo({
					url: "/pages/packageB/news/notice_info?id=" + item.id
				})
			},
			registerGetGidListener() {
				const that = this;
				uni.$on('getGid', function(data) {
					console.log('-----', data);
					that.page = 1;
					that.goryList = [];
					that.news_list = [];
					that.actList = [];
					that.carousel_list = [];
					that.association_id = data.gid;
					that.getNewsGoryList();
					// that.getActivityList();
					that.getAssociationIndex();
					that.getCarouselIndex();
				});
			},
			unregisterGetGidListener() {
				uni.$off('getGid');
			},
			openNewUrl(item) {
				//'/pages/packageB/news/new_info?id='+item.news_id
				console.log('item',item);
				if (item.wx_url == '') {
					uni.navigateTo({
						url: '/pages/packageB/news/new_info?id=' + item.news_id
					})
				} else {
					// uni.navigateTo({
					// 	url: '/pages/index/web?url=' + encodeURIComponent(item.wx_url),
					// })
					window.location.href = item.wx_url
				}
			},
			callPhone() {
				uni.makePhoneCall({
					phoneNumber: '15503791530', // 电话号码
					success: function() {
						console.log('拨打电话成功');
					},
					fail: function() {
						console.log('拨打电话失败');
					}
				});
			},
			msg() {
				return this.$store.state.msgCount;
			},
			getArticlePolicyListAll() {
				getArticlePolicyList({
						cid: 22
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list = res.data.ret;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openInfo(d) {
				uni.navigateTo({
					url: '/pages/packageA/info/policy_info?id=' + d.id,
				})
			},
			openRight() {
				uni.navigateTo({
					url: '/pages/packageA/info/policy',
				})
			},
			async getMsg() {
				var uid = uni.getStorageSync('uid');

				const res = await Mailcoent({
					member_id: uid
				});
				console.log(res);
				if (res.code == 1) {
					this.$store.commit('$tStore', {
						name: 'msgCount',
						value: res.data
					})
				} else {
					this.$store.commit('$tStore', {
						name: 'msgCount',
						value: 0
					})
				}
				console.log(store.state.msgCount);
			},
			confirm(d) {
				var info = d[0];
				console.log(info);
				store.commit('$tStore', {
					name: 'Gid',
					value: info.value
				})
				uni.setStorageSync('Gid', info.value);
				this.association_id = info.value;
				this.page = 1;
				this.goryList = [];
				this.news_list = [];
				this.actList = [];
				this.carousel_list = [];
				this.getNewsGoryList();
				// this.getActivityList();
				this.getAssociationIndex();
				this.getCarouselIndex();
				//this.$emit('childEvent', '修改后的数据');
			},
			getAssociationIndex() {
				associationIndex({
						page: 1,
						size: 200
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							key.push({
								id: 61,
								association_name: '洛阳市总商会'
							});
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.association_name,
								icon: item.association_image
							}));
							const foundNumber = transformedSelectList.find((element) => element.value == store.state
								.Gid);
							this.HomeTitle = foundNumber.label;
							this.selectList = transformedSelectList;
							//#ifdef MP-WEIXIN
							this.$parent.childEvent(foundNumber);
							//#endif
							// #ifdef  H5
							console.log('H5');
							this.$parent.$parent.$parent.$parent.childEvent(foundNumber);
							// #endif
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getCarouselIndex() {
				carouselIndex({
						association_id: store.state.Gid,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.carousel_list = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getActivityList() {
				activityList({
						association_id: store.state.Gid,
						page: 1,
						size: 10
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.actList = res.data.ret;
						} else {
							this.actList = [];
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getNewsGoryList() {
				newsGoryList({
						association_id: this.association_id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							key.unshift({
								id: 0,
								name: '最新'
							});
							this.goryList = key;
							this.type = 1;
							this.gory_id = 0;
							this.getNewsList();
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getNewsList() {
				newsList({
						association_id: store.state.Gid,
						type: this.type,
						gory_id: this.gory_id,
						page: this.page,
						size: this.size
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.news_list.push(...res.data.ret);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getTopicList() {
				newsList({
						association_id: 1,
						type: 0,
						gory_id: 60,
						page: 1,
						size: 10
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.actList.push(...res.data.ret);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			cardSwiper(d) {
				this.cardCur = d.detail.current;
			},
			ReachBottom() {
				console.log('home');
				//this.page = this.page + 1;
				//this.getNewsList();
				//this.getIndex();
			},
			change(e) {
				this.current = e;
				var info = this.goryList[e];
				this.gory_id = info.id;
				this.type = e === 0 ? 1 : 0;
				this.page = 1;
				this.news_list = [];
				this.getNewsList();
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			openApply() {
				var uid = uni.getStorageSync('uid');
				var userInfo = uni.getStorageSync('userInfo');
				if (!uid || !userInfo) {
					uni.showToast({
						title: '请登陆后查看',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				uni.navigateTo({
					url: '/pages/packageA/user/apply_in?association_id=' + this.association_id
				})

			},
			opentab() {
				uni.$emit('depId', {
					index: 1
				})
			},
			videoErrorCallback(e) {
				uni.showModal({
					content: e.target.errMsg,
					showCancel: false
				})
			},
			formatTime(time) {
				const [year, month, day] = time.split(' ')[0].split('-');
				return `${year}-${month}-${day}`;
			},
		}
	}
</script>


<style lang="scss" scoped>
	.my_tag {
		padding: 8rpx 15rpx;
		font-size: 20rpx;
		color: rgba(255, 255, 255, 1);
		border-radius: 0rpx 0rpx 22rpx 0rpx;
	}

	.rounded_corner {
		//border-radius: 16rpx 16rpx 0rpx 0rpx;
		padding-top: 20rpx;
	}

	.list {
		&__left {
			display: flex;
			align-items: center;
			justify-content: flex-start;

			&__icon,
			&__image {
				margin-right: 18rpx;
			}
		}

		&__right {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}
	}

	.list-icon-text,
	.list-image-text {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
</style>