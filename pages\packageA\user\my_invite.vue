<template>
	<view class="template-edit tn-safe-area-inset-bottom">
		<!-- 顶部自定义导航 -->
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'邀请入会'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>

		<view class="tn-safe-area-inset-bottom" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding: 50rpx">
				<view>
					<tn-button @click="openQrcode" backgroundColor="#82B2FF" height="80rpx" :shadow="true"
						fontColor="#ffffff" width="100%">生成二维码</tn-button>
				</view>
				<view style="margin-top: 40rpx">
					<tn-button open-type="share" backgroundColor="#82B2FF" height="80rpx" :shadow="true"
						fontColor="#ffffff" width="100%">转发到微信好友</tn-button>
				</view>
			</view>
			<!--			<view style="padding:20rpx 40rpx">-->
			<!--				<view style="font-size: 32rpx">我邀请的会员：30位</view>-->
			<!--				<view>-->
			<!--					<view class="tn-flex tn-flex-center tn-flex-col-center" style="padding: 30rpx;width: 100%;">-->
			<!--						<view>-->
			<!--							<image src="/static/def.png" style="width: 100rpx;height: 100rpx;border-radius: 50%;">-->
			<!--							</image>-->
			<!--						</view>-->
			<!--						<view style="width: 100%;">-->
			<!--							<view class="tn-flex tn-flex-col-center tn-flex-row-between">-->
			<!--								<view style="margin-left: 20rpx;color: #000000">-->
			<!--									<view style="font-size: 35rpx;">王小明</view>-->
			<!--								</view>-->
			<!--							</view>-->
			<!--						</view>-->
			<!--					</view>-->
			<!--				</view>-->
			<!--			</view>-->
		</view>
		<tn-popup :closeBtn="true" v-model="show" mode="center" width="500rpx" height="700rpx">
			<div style="text-align: center;padding: 30rpx;font-size: 32rpx;font-weight: 600;">使用二维码邀请入会</div>
			<view style="text-align: center;margin-top: 40rpx;">
				<image :src="qrcode_url" style="width: 400rpx;height: 400rpx;"></image>
			</view>
			<view style="text-align: center;margin-top: 40rpx;">
				<tn-button shape="round" backgroundColor="#82B2FF" fontColor="#ffffff"
					@click="saveBase64">保存图片</tn-button>
			</view>
		</tn-popup>
		<view class='tn-tabbar-height'></view>
	</view>
</template>

<script>
	import {
		getUserIndex,
		getQrcode
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				show: false,
				topCurrent: 0,
				uid: 0,
				userInfo: {},
				apiImgUrl: this.$store.state.imgUrl,
				qrcode_url: ''
			}
		},
		onShareAppMessage() {
			return {
				title: this.userInfo.nikename + '邀请你加入【' + this.userInfo.association_name + '】',
				path: '/pages/packageA/user/apply_in?id=' + this.uid + "&association_id=" + this.userInfo.association_id,
				imageUrl: this.apiImgUrl + this.userInfo.association_image,
			}
		},
		onShareTimeline() { // 分享到朋友圈
			return {
				title: this.userInfo.nikename + '邀请你加入【' + this.userInfo.association_name + '】',
				path: '/pages/packageA/user/apply_in?id=' + this.uid + "&association_id=" + this.userInfo.association_id,
				imageUrl: this.apiImgUrl + this.userInfo.association_image,
			}
		},
		onLoad() {
			var u = uni.getStorageSync('uid');
			this.uid = u;
			this.getUserInfo();
		},
		methods: {
			saveBase64() {
				wx.saveImageToPhotosAlbum({
					filePath: this.qrcode_url,
					success: function(res) {
						wx.showToast({
							title: '保存成功',
						})
					},
					fail: function(err) {
						console.log(err, '失败')
					}
				})
			},
			openQrcode() {
				uni.showLoading({
					title: '生成中',
					mask: true,
				});
				var that = this;
				uni.request({
					url: store.state.apiUrl + '/move/login/getUnlimitedQRCode',
					method: 'POST',
					data: {
						path: 'pages/packageA/user/apply_in',
						scene: "id=" + this.uid + "&association_id=" + this.userInfo.association_id
					},
					responseType: 'arraybuffer',
					arraybuffer: true,
					success: (result) => {
						const url = 'data:image/png;base64,' + uni.arrayBufferToBase64(result.data);
						that.base64ToImage(url);
					}
				})
			},
			base64ToImage(base64Data) {
				var that = this;
				const fs = uni.getFileSystemManager();
				var filePath = wx.env.USER_DATA_PATH + '/qrcode.jpg';
				const base64 = base64Data.split(',')[1]; // 获取base64字符串部分
				fs.writeFile({
					filePath: filePath,
					data: base64,
					encoding: 'base64',
					success: (res) => {
						that.qrcode_url = filePath;
						console.log('图片保存成功', filePath);
						// 成功回调
						that.show = true;
						uni.hideLoading()
					},
					fail: (err) => {
						console.error('图片保存失败', err);
						uni.hideLoading()
						// 失败回调
					}
				});
			},
			getUserInfo() {
				getUserIndex({
						member_b_id: this.uid,
						member_id: this.uid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.userInfo = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 底部悬浮按钮 start*/
	.tn-tabbar-height {
		min-height: 100rpx;
		height: calc(120rpx + env(safe-area-inset-bottom) / 2);
	}

	.tn-footerfixed {
		position: fixed;
		width: 100%;
		bottom: calc(30rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);

	}

	/* 底部悬浮按钮 end*/

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 20rpx 20rpx 5rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/
</style>