<template>
	<view>
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="false" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between">
				<view class="tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="goBack()">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:'惠企政策详情'}]" :current="topCurrent" activeColor="#000" :bold="false"
							:fontSize="36"></tn-tabs>
					</view>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}" style="padding-bottom: 60rpx;">
			<view style="padding: 30rpx;">
				<view style="font-size: 34rpx;font-weight: 600;text-align: center;">{{info.title}}</view>
				<view style="margin-top: 20rpx;">
					<view style="color: #999999;font-size: 28rpx;text-align: center;">
						<text>业务科室：{{info.source}}</text>
						<text style="margin-left: 60rpx;">联系方式：{{info.phone}}</text>
					</view>
				</view>
				<view v-html="info.content" style="margin-top: 30rpx;"></view>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		getArticlePolicyInfo
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				info: {},
				id: 0
			}
		},
		onLoad(d) {
			console.log(d);
			this.id = d.id;
			this.articlePolicyInfo();
		},
		methods: {
			articlePolicyInfo() {
				getArticlePolicyInfo({
						id: this.id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.info = res.data;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>

</style>