import store from "../store/index.js" //引入vuex

//#ifdef H5
const jweixin = require('jweixin-module')
// H5分享
export const wxshare = options => {
	uni.request({
		// url: 'http://ysh.0rui.cn/index.php',
		url: 'https://ysx.0rui.cn/index.php',
		method: 'POST',
		data: options.data || '',
		header: { //配置请求头
			'content-type': 'application/x-www-form-urlencoded',
		},
		success: res => {
			// console.log('res.data', res.data.data)
			jweixin.config({
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: res.data.appId, // 必填，公众号的唯一标识
				timestamp: res.data.timestamp, // 必填，生成签名的时间戳
				nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
				signature: res.data.signature, // 必填，签名
				// surl: res.data.surl, //自己添加的，debug为true的时候可以网页打印出对应的URL是否正确
				jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData',
					'openLocation'
				]
			})
			setTimeout(() => {
				jweixin.ready(() => {
					//自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
					jweixin.updateAppMessageShareData({
						title: options.share_data.title || '洛阳市青年户外协会掌上云册', // 分享标题
						desc: options.share_data.desc || '洛阳市青年户外协会掌上云册', // 分享描述
						link: options.share_data.link, // 分享链接
						// imgUrl: options.share_data.imgUrl ||
						// 	'http://ysh.0rui.cn/static/ser.png', // 分享图标
						imgUrl: options.share_data.imgUrl ||
							'https://ysx.0rui.cn/static/ser.png',
						success: res => {},
						cancel: function() {}
					})
					//自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容（1.4.0）
					jweixin.updateTimelineShareData({
						title: options.share_data.title || '洛阳市青年户外协会掌上云册', // 分享标题
						link: options.share_data.link, // 分享链接
						// imgUrl: options.share_data.imgUrl ||
						// 	'http://ysh.0rui.cn/static/ser.png', // 分享图标
						imgUrl: options.share_data.imgUrl ||
							'https://ysx.0rui.cn/static/ser.png', 
						success: res => {},
						cancel: function() {}
					})
				})
			}, 500)

		},
		fail: err => {
			uni.showToast({
				title: '请求失败',
				icon: 'error'
			})
		}
	})
}
//#endif