<template>
	<view style="letter-spacing: 1rpx;background: #ebf4f7;min-height: 100vh;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="false" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;width: 85%;">
					<!-- <view>智慧云商协</view> -->
					<view>洛阳市青年户外协会掌上云册</view>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view
				style="z-index: 100;;position: fixed;;width: 100%;margin: 0 auto;background-color: #ffffff;padding: 0rpx 50rpx;">
				<tn-tabs :list="list" activeColor="#3f8bf2" backgroundColor="#FFFFFF" :isScroll="false"
					:current="current" name="name" @change="change"></tn-tabs>
			</view>
			<view style="padding: 30rpx;margin-top: 90rpx;">
				<view class="tn-flex tn-flex-col-center" style="margin-bottom: 40rpx;">
					<view @click="changeXs(0)"
						:style="{'color': if_xianshi==0?'#ffffff':'#000000','background-color': if_xianshi==0?'#3f8bf2':'#FFF','border-radius': '50rpx','padding': '10rpx 30rpx'}">
						待审核
					</view>
					<view @click="changeXs(1)"
						:style="{'color': if_xianshi==1?'#ffffff':'#000000','background-color': if_xianshi==1?'#3f8bf2':'#FFF','border-radius': '50rpx','padding': '10rpx 30rpx','margin':'0rpx 20rpx'}">
						已通过
					</view>
					<view @click="changeXs(2)"
						:style="{'color': if_xianshi==2?'#ffffff':'#000000','background-color': if_xianshi==2?'#3f8bf2':'#FFF','border-radius': '50rpx','padding': '10rpx 30rpx'}">
						已拒绝
					</view>
				</view>
				<view v-for="item in all_list"
					@click="openUrl('/pages/packageA/user/user_auditing_info?id='+item.id+'&type='+item.type)"
					style="margin-bottom: 30rpx;box-shadow: 0rpx 10rpx 10rpx #d2e0f2;background: #ffffff;border-radius: 20rpx;padding: 30rpx 20rpx;position: relative;">
					<view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between">
						<view>
							<text class="tn-icon-my-fill" style="color: #3f8bf2;"></text>
							<text style="margin-left: 10rpx;">申请人</text>
							<text style="margin-left: 50rpx;">{{item.nikename}}</text>
						</view>
						<view>
							<text style="color: #3f8bf2;">查看</text>
						</view>
					</view>
					<view style="width: 100%;margin: 20rpx auto;border: 1rpx solid #d9d9d961;"></view>
					<view>
						<text>申请事项</text>
						<text style="margin-left: 50rpx;">{{item.type==1?'修改申请':'入会申请'}}</text>
					</view>
					<view style="margin: 25rpx 0rpx;">
						<text>审核状态</text>
						<text style="margin-left: 50rpx;" v-if="item.if_xianshi==0">待审核</text>
						<text style="margin-left: 50rpx;" v-if="item.if_xianshi==1">已通过</text>
						<text style="margin-left: 50rpx;" v-if="item.if_xianshi==2">已拒绝</text>
					</view>
					<view>
						<text>申请时间</text>
						<text style="margin-left: 50rpx;">{{item.createtime}}</text>
					</view>
					<image v-if="item.if_xianshi==0" src="/static/sh.png"
						style="width: 140rpx;height: 140rpx;position: absolute;top: 140rpx;right: 30rpx;">
					</image>
					<image v-if="item.if_xianshi==1" src="/static/tg.png"
						style="width: 140rpx;height: 140rpx;position: absolute;top: 140rpx;right: 30rpx;">
					</image>
					<image v-if="item.if_xianshi==2" src="/static/jj.png"
						style="width: 140rpx;height: 140rpx;position: absolute;top: 140rpx;right: 30rpx;">
					</image>
					<view v-if="item.if_xianshi==0" class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-around"
						style="color: #FFF;text-align: center;margin-top: 40rpx;">
						<view @click.stop="openModal(item,2)"
							style="color: #ffffff;line-height: 76rpx;width: 240rpx;height: 76rpx;background: #d54941;border-radius: 76rpx;">
							拒绝</view>
						<view @click.stop="openModal(item,1)"
							style="color: #ffffff;line-height: 76rpx;width: 240rpx;height: 76rpx;background: #3464ec;border-radius: 76rpx;">
							通过</view>
					</view>
				</view>
			</view>
		</view>
		<tn-modal v-model="show" :title="title" :content="content" :button="button" @click="dian"></tn-modal>
	</view>
</template>

<script>
	import {
		getAuditing,
		auditingUpadte
	} from '@/util/api.js';
	import store from "@/store";
	export default {
		data() {
			return {
				list: [{
					name: '全部申请'
				}, {
					name: '信息修改'
				}, {
					name: '入会申请',
				}],
				page: 1,
				all_list: [],
				current: 0,
				if_xianshi: 0,
				show: false,
				title: '提示信息',
				content: '提示信息的内容',
				button: [{
						text: '取消',
						backgroundColor: '#F4F4F4',
						fontColor: '#000000',
						plain: true,
						shape: 'round'
					},
					{
						text: '确定',
						backgroundColor: '#548ceb',
						fontColor: '#FFFFFF'
					}
				],
				key: 0,
				key_info: {}
			}
		},
		onLoad() {
			var that = this;
			this.getAuditingList();
			uni.$on('onload', function(data) {
				that.page = 1;
				that.all_list = [];
				that.getAuditingList();
			})
		},
		onReachBottom() {
			this.page += 1;
			this.getAuditingList();
		},
		methods: {
			setAuditing() {
				var user_info = uni.getStorageSync('userInfo');
				var member_id = this.key_info.type == 1 ? this.key_info.member_id : this.key_info.id;
				var content = '';
				if (this.key == 1) {
					content = this.key_info.type == 1 ? '您提交的资料修改申请已通过审核！' : '您提交的入会申请已通过审核！';
				} else {
					content = this.key_info.type == 2 ? '您提交的资料修改申请已被拒绝！' : '您提交的入会申请已被拒绝！';
				}
				auditingUpadte({
						association_id: user_info.association_id,
						member_id: member_id,
						if_xianshi: this.key,
						type: this.key_info.type,
						content: content,
						id: this.key_info.id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
							this.page = 1;
							this.all_list = [];
							this.getAuditingList();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
						this.show = false;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			dian(d) {
				if (d.index == 1) {
					this.setAuditing();
				} else {
					this.show = false;
				}
			},
			openModal(info, key) {
				console.log(key);
				this.key_info = info;
				this.key = key;
				this.show = true;
				this.title = key == 1 ? '通过申请' : '拒绝申请';
				this.content = key == 1 ? '确认要通过申请吗？' : '确认要拒绝申请吗？';
			},
			change(d) {
				this.current = d;
				this.page = 1;
				this.if_xianshi = 0;
				this.all_list = [];
				this.getAuditingList();
			},
			changeXs(d) {
				this.page = 1;
				this.all_list = [];
				this.if_xianshi = d;
				this.getAuditingList();
			},
			getAuditingList() {
				var user_info = uni.getStorageSync('userInfo');
				getAuditing({
						association_id: user_info.association_id,
						page: this.page,
						size: 10,
						if: this.current,
						if_xianshi: this.if_xianshi,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.all_list.push(...res.data);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>

</style>
