<template>
	<view style="background-color: #F7F7F7;min-height: 100vh;">
		<tn-nav-bar customBack :bottomShadow="false" backgroundColor="#ffffff">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
				<view style="color: #000000;;text-align: left;font-size: 36rpx;font-weight: 600;">
					<text>详情</text>
				</view>
			</view>
			<view slot="back" class='tn-custom-nav-bar__back' style="padding-left: 20rpx;" @click="goBack">
				<image src="/static/h_back.png" style="width: 60rpx;height: 60rpx;"></image>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding: 30rpx;">
				<view style="background-color: #fff;border-radius: 18rpx;padding: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image v-if="info.photo_image !='' && info.photo_image!=null" :src="apiImgUrl+info.photo_image" style="border-radius: 50%;width: 50rpx;height: 50rpx;vertical-align: middle;"
							mode="aspectFill"></image>
							<image v-if="info.photo_image =='' || info.photo_image==null" src="/static/def.png" style="border-radius: 50%;width: 50rpx;height: 50rpx;vertical-align: middle;"
								mode="aspectFill"></image>
						<text
							style="color: #9B9B9B;vertical-align: middle;margin-left: 10rpx;font-size: 28rpx;font-weight: 400;">{{info.member_name}}</text>
					</view>
					<view class="tn-flex" style="margin-top: 30rpx;">
						<view>
							<image src="/static/wen.png" style="width: 38rpx;" class="no-img" mode="widthFix">
							</image>
						</view>
						<view style="margin-left: 20rpx;font-size: 28rpx;font-weight: 400;">
							{{info.issue_names}}
						</view>
					</view>
					<view v-if="info.problem_images!=null && info.problem_images!=''"
						style="text-align: center;margin-top: 20rpx;">
						<image @click="previewImage(0)" :src="apiImgUrl+info.problem_images[0]"
							style="width: 200rpx;height: 200rpx;border-radius:18rpx 0rpx 0rpx 18rpx;"></image>
						<image @click="previewImage(1)" :src="apiImgUrl+info.problem_images[1]"
							style="width: 200rpx;height: 200rpx;margin: 0rpx 10rpx;">
						</image>
						<image @click="previewImage(2)" :src="apiImgUrl+info.problem_images[2]"
							style="width: 200rpx;height: 200rpx;border-radius:0rpx 18rpx 18rpx 0rpx;"></image>
					</view>

					<view class="tn-flex tn-flex-col-center tn-flex-row-between"
						style="margin-top: 30rpx;font-size: 26rpx;font-weight: 400;color: #9B9B9B;">
						<view>{{info.question_time}}</view>
						<view>
							<text class="tn-icon-eye"></text>
							<text style="margin-left: 5rpx;">{{info.click_count}}</text>
						</view>
					</view>
				</view>
				<view v-if="info.answer_content!=null && info.answer_content!=''"
					style="background-color: #fff;border-radius: 18rpx;padding:30rpx;">
					<view class="tn-flex">
						<view>
							<image src="/static/da.png" style="width: 35rpx;" class="no-img" mode="widthFix">
							</image>
						</view>
						<view 
							style="margin-left: 20rpx;font-size: 28rpx;font-weight: 400;color: #9B9B9B;">
							<view v-html="info.answer_content"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getNwwbInfo
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				apiImgUrl: store.state.imgUrl,
				id: 0,
				info: {}
			}
		},
		onLoad(item) {
			this.id = item.id;
			this.getInfo();
		},
		methods: {
			previewImage(index) {
				uni.previewImage({
					current: index,
					urls: this.info.problem_images
				});
			},
			getInfo() {
				getNwwbInfo({
						id: this.id
					})
					.then(res => {
						console.log(res);
						this.info = res.data;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style>

</style>