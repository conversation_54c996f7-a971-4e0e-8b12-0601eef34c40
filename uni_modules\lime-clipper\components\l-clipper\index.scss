$clipper-edge-border-width: 6rpx !default;
$clipper-confirm-color: #07c160 !default;

.flex-auto {
	flex:auto
}
	
.bg-transparent {
	background-color: rgba(0, 0, 0, 0.9);
	transition-duration: 0.35s;
}

.lime-clipper {
	width: 100vw;
	height: calc( 100vh - var(--window-top));
	background-color: rgba(0, 0, 0, 0.9);
	position: fixed;
	top: var(--window-top);
	left: 300vw;
	z-index: 1;
	&.open {
		left: 0;
	}	
	&-mask {
		position: relative;
		z-index: 2;
		pointer-events: none;
	}
	&__content {
		pointer-events: none;
		position: absolute;
		border: 1rpx solid rgba(255,255,255,.3);
		box-sizing: border-box;
		box-shadow: rgba(0, 0, 0, 0.5) 0 0 0 80vh;
		background: transparent;	
		// transition-duration 0.35s
		// transition-property left,top
		&::before,&::after {
			content: '';
			position: absolute;
			border: 1rpx dashed rgba(255,255,255,.3);
		}
			
		&::before {
			width: 100%;
			top: 33.33%;
			height: 33.33%;
			border-left: none;
			border-right: none;
		}
			
		&::after {
			width: 33.33%;
			left: 33.33%;
			height: 100%;
			border-top:none;
			border-bottom: none;
		}
			
	}
		
	&__edge {
		position: absolute;
		// left 6rpx
		width: 34rpx;
		height: 34rpx;
		border: $clipper-edge-border-width solid #ffffff;
		pointer-events: auto;
		&::before {
			content: '';
			position: absolute;
			width: 40rpx;
			height: 40rpx;
			background-color: transparent;
		}
			
		&:nth-child(1) {
			left: - $clipper-edge-border-width;
			top: - $clipper-edge-border-width;
			border-bottom-width: 0 !important;
			border-right-width: 0 !important;
			&:before {
				top: -50%;
				left: -50%;
			}
		}
			
		&:nth-child(2) {
			right: - $clipper-edge-border-width;
			top: - $clipper-edge-border-width;
			border-bottom-width: 0 !important;
			border-left-width: 0 !important;
			&:before {
				top: -50%;
				left: 50%;
			}
				
		}
			
		&:nth-child(3) {
			left: - $clipper-edge-border-width;
			bottom: - $clipper-edge-border-width;
			border-top-width: 0 !important;
			border-right-width: 0 !important;
			&:before {
				bottom: -50%;
				left: -50%;
			}
		}
			
		&:nth-child(4) {
			right: - $clipper-edge-border-width;
			bottom: - $clipper-edge-border-width;
			border-top-width: 0 !important;
			border-left-width: 0 !important;
			&:before {
				bottom: -50%;
				left: 50%;
			}
		}
			
	}
		
	&-image {
		width: 100%;
		max-width: inherit;
		border-style: none;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		-webkit-backface-visibility: hidden;
		backface-visibility: hidden;
		transform-origin: center;
	}
		
	&-canvas {
		position: fixed;
		z-index: 10;
		left: -200vw;
		top: -200vw;
		pointer-events: none;
	}
		
	&-tools {
		position: absolute;
		left: 0;
		bottom: 10px;
		width: 100%;
		z-index: 99;
		color: #fff;
		&__btns {
			font-weight: bold;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			padding: 20rpx 40rpx;
			box-sizing: border-box;
			.cancel {
				width: 112rpx;
				height: 60rpx;
				text-align: center;
				line-height: 60rpx;
			}
				
			.confirm {
				width: 112rpx;
				height: 60rpx;
				line-height: 60rpx;
				background: var(--lime-clipper-confirm-color, $clipper-confirm-color);
				border-radius: 6rpx;
				text-align: center;
			}
				
			image {
				display: block;
				width: 60rpx;
				height: 60rpx;
			}
		}	
	}		
}
	
