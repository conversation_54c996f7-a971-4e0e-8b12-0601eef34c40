## 1.0.1（2023-06-12）
- fix: 修复vue3 QQ小程序无法生成问题
## 1.0.0（2023-06-01）
- feat: 支持 vue3 并把 `stylus` 改成 `scss`
## 0.9.0（2022-12-12）
- chore: 更新文档
- feat: 当缩放为1时 旋转自适应裁剪框
## 0.8.0（2022-11-02）
- fix: 修复 正方形图片 不充满裁剪框的BUG
## 0.7.0（2021-07-10）
- chore: 统一css3 变量 命名规范
- chore: 更新文档
## 0.6.9（2021-07-09）
- chore: 统一命名规范，无须主动引入组件
- fix: 修复第一次传入图片路径无自适应问题
- fix: 修复安卓方向导致无法正确得到尺寸问题
## 0.6.8（2021-06-26）
- fix: 修复 钉钉小程序 返回图片为 `null` 问题
## 0.6.7（2021-06-19）
- fix: 修复`@touchstart`书写错误
## 0.6.6（2021-06-19）
- fix: 修复支付宝引用图标的错误
## 0.6.5（2021-06-14）
- fix: 修复锁定比例失效问题
## 0.6.4（2021-04-16）
- 修复因mode引起的高度异常
## 0.6.3（2021-04-13）
- 测试到微信小程序无法进第二次`draw`，故更新使用`v-model`的方式，显示隐藏组件。
- 原先使用v-if显示隐藏组件依然可以使用，若发现无法二次`draw`请使用`v-model`
```html
<l-clipper v-model="show" />
```
```js
data: () => ({
	show: false
})
```
## 0.6.2（2021-03-26）
1、修复图片初始化BUG
2、增加`fileType`属性，默认值为`png`
## 0.6.1（2021-03-10）
- 给事件添加阻止冒泡
## 0.6.0（2021-03-06）
增加`source`属性，`Object`类型，key为图片来源类型，value为选项说明。<br>
```js
// source 默认值 
{
	album: '从相册中选择',
	camera: '拍照',
	// #ifdef MP-WEIXIN
	message: '从微信中选择'
	// #endif
}
```
## 0.5.0（2021-02-26）
- 调整为uni_modules目录规范
