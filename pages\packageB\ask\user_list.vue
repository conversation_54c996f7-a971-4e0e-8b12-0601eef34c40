<template>
	<view style="background-color: #F5F5F5;height: 100vh;">
		<tn-nav-bar customBack :bottomShadow="false" backgroundColor="#ffffff">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
				<view style="color: #000000;;text-align: left;font-size: 36rpx;font-weight: 600;">
					<text>信息审核</text>
				</view>
			</view>
			<view slot="back" class='tn-custom-nav-bar__back' style="padding-left: 20rpx;" @click="goBack">
				<image src="/static/h_back.png" style="width: 60rpx;height: 60rpx;"></image>
			</view>

		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="background-color: #fff;">
				<tn-tabs :list="List" :isScroll="false" :activeItemStyle="{fontSize:'30rpx',fontWeight:'600'}"
					activeColor="#000000" inactiveColor="#9B9B9B"
					:barStyle="{color:'#2368F2',backgroundColor:'#2368F2'}" :current="current" name="name"
					@change="change" :fontSize="28"></tn-tabs>
			</view>
			<view style="padding: 30rpx;">
				<view v-for="item in userList"
					style="background-color: #fff;border-radius: 18rpx;padding: 30rpx;margin-bottom: 30rpx;position: relative;">
					<image v-if="current==0" src="/static/ask_dai.png"
						style="width: 100rpx;position: absolute;right: 0;top: 0;" mode="widthFix" class="no-img">
					</image>
					<image v-if="current==1" src="/static/ask_ok.png"
						style="width: 100rpx;position: absolute;right: 0;top: 0;" mode="widthFix" class="no-img">
					</image>
					<image v-if="current==2" src="/static/ask_no.png"
						style="width: 100rpx;position: absolute;right: 0;top: 0;" mode="widthFix" class="no-img">
					</image>
					<view>
						<image v-if="item.photo_image !='' && item.photo_image!=null" :src="apiImgUrl+item.photo_image"
							style="border-radius: 50%;width: 50rpx;height: 50rpx;vertical-align: middle;"
							mode="aspectFill"></image>
						<image v-if="item.photo_image =='' || item.photo_image==null" src="/static/def.png"
							style="border-radius: 50%;width: 50rpx;height: 50rpx;vertical-align: middle;"
							mode="aspectFill"></image>
						<text
							style="color: #9B9B9B;vertical-align: middle;margin-left: 10rpx;font-size: 28rpx;font-weight: 400;">{{item.member_name}}</text>
					</view>
					<view class="tn-flex" style="margin-top: 30rpx;">
						<view>
							<image src="/static/wen.png" style="width: 38rpx;" class="no-img" mode="widthFix">
							</image>
						</view>
						<view style="margin-left: 20rpx;font-size: 28rpx;font-weight: 400;">
							{{item.issue_names}}
						</view>
					</view>
					<view v-if="item.problem_images!=null && item.problem_images!=''"
						style="text-align: center;margin-top: 20rpx;">
						<image @click="previewImage(0)" :src="apiImgUrl+item.problem_images[0]"
							style="width: 200rpx;height: 200rpx;border-radius:18rpx 0rpx 0rpx 18rpx;"></image>
						<image @click="previewImage(1)" :src="apiImgUrl+item.problem_images[1]"
							style="width: 200rpx;height: 200rpx;margin: 0rpx 10rpx;">
						</image>
						<image @click="previewImage(2)" :src="apiImgUrl+item.problem_images[2]"
							style="width: 200rpx;height: 200rpx;border-radius:0rpx 18rpx 18rpx 0rpx;"></image>
					</view>

					<view class="tn-flex tn-flex-col-center tn-flex-row-between"
						style="margin-top: 30rpx;font-size: 26rpx;font-weight: 400;color: #9B9B9B;">
						<view>{{info.question_time}}</view>
						<!-- <view>
							<text class="tn-icon-eye"></text>
							<text>133</text>
						</view> -->
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getNwwbUser
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				apiImgUrl: store.state.imgUrl,
				current: 0,
				List: [{
					'name': '待审核',
					type: 1,
				}, {
					'name': '已通过',
					type: 2
				}, {
					'name': '未通过',
					type: 3
				}],
				status: 1,
				userList: [],
				page: 1
			}
		},
		onLoad() {
			this.getUserList();
		},
		onReachBottom() {
			this.page += 1;
			this.getUserList();
		},
		methods: {
			change(e) {
				this.userList = [];
				this.page = 1;
				this.current = e;
				this.status = this.List[e].type;
				this.getUserList();
			},
			getUserList() {
				getNwwbUser({
						status: this.status,
						page: this.page,
						size: 10
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.userList.push(...res.data);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style scoped>

</style>