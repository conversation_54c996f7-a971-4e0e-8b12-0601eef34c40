<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'详情'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding: 30rpx;">
				<view style="text-align: center;font-size:40rpx;font-weight: 600">{{info.news_title}}</view>
				<view style="margin-top: 30rpx;color: #999;">
					<text>时间：{{info.showtime}}</text>
					<text style="margin-left: 20rpx;">来源：{{info.news_source}}</text>
				</view>
				<view style="width: 100%;height: 2rpx;background-color: #eee;margin-top: 30rpx;"></view>
				<view>
					<!--					<view style="margin-top: 20rpx;position: relative;">-->
					<!--						<image src="/static/b1.png"-->
					<!--							style="width: 100rpx;height: 100rpx;position: absolute;left: 0;right: 0;top: 38%;z-index: 10;margin: 0 auto;">-->
					<!--						</image>-->
					<!--						<image src="/static/t7.jpg" style="width: 100%;border-radius: 20rpx;" mode="widthFix">-->
					<!--						</image>-->
					<!--					</view>-->
					<view class="ks_html" style="line-height: 50rpx;margin-top: 20rpx;">
						<div v-html="info.news_content"></div>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		newsInfo,
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				topCurrent: 0,
				id: 0,
				info: {},
				apiImgUrl: this.$store.state.imgUrl,
			}
		},
		// mounted() {
		// 	var id = window.location.href.split("=")[1];
		// 	this.id = id;
		// 	this.getNewsInfo();
		// },
		onLoad(d) {
			console.log(d);
			this.id = d.id;
			if (typeof(d.gid) != 'undefined') {
				store.commit('$tStore', {
					name: 'Gid',
					value: d.gid
				})
				uni.setStorageSync('Gid', d.gid);
			}
			this.getNewsInfo();
		},
		onShareAppMessage() { // 分享到微信好友
			// 更多参数配置，参考文档
			return {
				title: this.info.news_title,
				path: '/pages/packageB/news/new_info?id=' + this.info.news_id + '&gid=' + this.info.association_id,
				imageUrl: this.apiImgUrl + this.info.news_image,
			}
		},
		onShareTimeline() { // 分享到朋友圈
			return {
				title: this.info.news_title,
				path: '/pages/packageB/news/new_info?id=' + this.info.news_id + '&gid=' + this.info.association_id,
				imageUrl: this.apiImgUrl + this.info.news_image,
			}
		},
		methods: {
			getNewsInfo() {
				newsInfo({
						association_id: store.state.Gid,
						news_id: this.id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							if (key.news_content.indexOf("<img") != -1) {
								key.news_content = getApp().addWidthToImages(key.news_content);
							}
							this.info = key;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>
	.ks_html img {
		width: 100% !important;
	}
</style>
