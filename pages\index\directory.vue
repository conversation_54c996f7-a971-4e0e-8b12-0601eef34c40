<template>
	<!-- 通讯录页面 -->
	<view style="background-color: #EBF4F7;letter-spacing: 1rpx;" @touchmove="preventTouchMove">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:HomeTitle+'通讯录'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="30"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">

			<!-- <view class="tn-flex tn-flex-col-center tn-flex-row-between" @click="selectShow= true"
				style="padding: 30rpx;">
				<view>
					<text>{{HomeTitle}}</text>
					<text class="tn-icon-down-triangle"></text>
				</view>
				<view>
					<text>筛选</text>
				</view>
			</view> -->
		</view>
		<view>
			<!-- <view class="tn-flex tn-flex-row-between tn-flex-col-center" style="padding: 0px 30rpx;height: 140rpx;"> -->
			<view class="tn-flex tn-flex-row-between tn-flex-col-center" style="padding: 0px 30rpx;height: 40rpx;">
				<!--<view class="justify-content-item align-content-item" style="width: 100%;">
					<view class="tn-flex tn-flex-col-center"
						style="border-radius: 100rpx;padding: 10rpx 20rpx 10rpx 20rpx;width: 95%;background-color: #ffffff;">
						<text
							class="tn-icon-search justify-content-item tn-padding-right-xs tn-color-gray tn-text-lg"></text>
						<input v-model="serach_content" class="justify-content-item" placeholder="会员姓名/手机号" name="input"
							placeholder-style="color:#AAAAAA" style="width: 90%;"></input>
					</view>
				</view>
				 <view class="align-content-item">
					<view class="justify-content-item tn-text-center">
						<tn-button @click="serach_do" backgroundColor="#3668fc" shape="round" padding="20rpx 20rpx"
							width="150rpx">
							<text class="tn-color-white">搜 索</text>
						</tn-button>

					</view>
				</view> -->
			</view>
		</view>
		<view class="tn-classify__container">

			<view class="tn-classify__container__wrap tn-flex tn-flex-nowrap tn-flex-row-around"
				:style="{backgroundColor:'#EBF4F7',height:my_page+'px'}">
				<!-- 左边容器 -->
				<scroll-view scroll-y class="tn-classify__left-box left-width">
					<view v-for="(item, index) in industry_list" :key="index" :id="`tabbar_item_${index}`"
						style="padding-left: 20rpx;" class="tn-classify__tabbar__item tn-flex tn-flex-col-center"
						:class="[tabbarItemClass(index)]" @tap.stop="clickClassifyNav(index)">
						<view class="tn-classify__tabbar__item__title">{{ item.industry_name }}</view>
					</view>
				</scroll-view>

				<!-- 右边容器 -->
				<scroll-view class="tn-classify__right-box" @scroll="getScroll" :scroll-top="top" scroll-y
					style="width: 72%">
					<block>
						<view class="tn-classify__content">
							<!-- 分类内容子栏目 -->
							<view class="tn-classify__content__sub-classify__content " style="padding-bottom: 120rpx;">
								<view v-for="(item,index) in list" :key="index">
									<view v-if="item.neirong"
										style="font-weight: 300;;background-color: #EBF4F7;color:#4AA2EF;width: 100%;height: 60rpx;line-height: 60rpx;text-align: center;">
										{{item.position_name}}
									</view>
									<view v-if="item.neirong" v-for="(v,k) in item.neirong"
										@click="tn('/pages/packageA/user/my_card?id='+v.member_id)"
										class="tn-classify__content__sub-classify__content__item tn-flex tn-flex-center  tn-flex-col-center">
										<!-- 标题，有需要可以显示出来 -->
										<view style="width: 100rpx;height: 100rpx">
											<image v-if="v.gender == 1" src="https://ysx.0rui.cn/h5/static/man.png"
												style="width: 100rpx;height: 100rpx;border-radius:50%;">
											</image>
											<image v-else src="https://ysx.0rui.cn/h5/static/weman.png"
												style="width: 100rpx;height: 100rpx;border-radius:50%;">
											</image>
											<!-- <image v-if="v.gender == 1" src="@/static/man.png"
												style="width: 100rpx;height: 100rpx;border-radius:50%;">
											</image>2
											<image v-else src="@/static/weman.png"
												style="width: 100rpx;height: 100rpx;border-radius:50%;">
											</image> -->
											 <!-- <image v-else :src="apiImgUrl+v.photo_image"
												style="width: 100rpx;height: 100rpx;border-radius:50%;">
											</image> -->
										</view>
										<view style="margin-left: 20rpx;">
											<view style="font-size: 28rpx;">
												<text>{{v.nikename}}</text>
												<text
													style="margin-left: 50rpx;">{{v.nation==null||v.nation==''?'':v.nation}}</text>
											</view>
											<view class="tn-text-ellipsis-2"
												style="font-size: 24rpx;margin-top: 10rpx;">
												<text>{{v.enterprise_name==null?'':v.enterprise_name}}</text>
											</view>
										</view>
									</view>
								</view>
								<view v-if="list.length==0" style="text-align: center;padding: 20rpx">
									暂无
								</view>
							</view>
						</view>
					</block>
				</scroll-view>
			</view>
		</view>
		<!--		<view @click="tn('/pages/index/apply_in')" style="line-height: 40rpx;text-align: center;position: fixed;bottom:15%;right: 20rpx;width: 120rpx;height: 120rpx;border-radius: 50%;background: linear-gradient(270deg, #EE7E45, #EE9657);-->
		<!--box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.2);">-->
		<!--			<view style="color: #fff;letter-spacing: 2rpx;padding: 20rpx;font-size: 32rpx;">申请入会-->
		<!--			</view>-->
		<!--		</view>-->
		<!-- <tn-select :safeAreaInsetBottom="true" v-model="selectShow" mode="single" :list="selectList"
			@confirm="confirm"></tn-select> -->
	</view>
</template>

<script>
	import {
		addressList,
		associationIndex,
		newIndustryIndex
	} from '@/util/api.js';
	import store from "@/store";
	export default {
		data() {
			return {
				serach_content: '',
				list: [],
				topCurrent: 0,
				tabbarIndex: 0,
				// 分类菜单item的信息
				tabbarItemInfo: [],
				// scrollView的top值
				scrollViewBasicTop: 0,
				// scrollView的高度
				scrollViewHeight: 0,
				// 左边scrollView的滚动高度
				leftScrollViewTop: 0,
				// 右边scrollView的滚动高度
				rightScrollViewTop: 0,
				// 当前选中的tabbar序号
				currentTabbarIndex: 0,
				apiImgUrl: this.$store.state.imgUrl,
				industry_list: [],
				leftId: 0,
				selectShow: false,
				HomeTitle: '',
				selectList: [],
				gid: store.state.Gid,
				gname: '',
				top: 0,
				my_page: 0
			}
		},
		computed: {
			tabbarItemClass() {
				return index => {
					if (index === this.currentTabbarIndex) {
						return 'tn-classify__tabbar__item--active tn-bg-white'
					} else {
						let clazz = ''
						if (this.currentTabbarIndex > 0 && index === this.currentTabbarIndex - 1) {
							clazz += ' tn-classify__tabbar__item--active--prev'
						}
						if (this.currentTabbarIndex < this.industry_list.length && index === this.currentTabbarIndex +
							1) {
							clazz += ' tn-classify__tabbar__item--active--next'
						}
						return clazz
					}
				}
			}
		},
		onLoad(d) {
			console.log(d.id);
			if (typeof(d.id) != 'undefined') {
				this.gid = d.id;
			}
			this.getIndustryList();
			this.getAssociationIndex();

		},
		methods: {
			preventTouchMove() {

			},
			serach_do() {
				this.list = [];
				this.leftId = 0;
				this.currentTabbarIndex = 0;
				console.log(this.leftId);
				this.getAddressList();
			},
			getScroll(d) {
				this.top = d.scrollTop;
			},
			onload() {
				console.log(this.gid);
				console.log(store.state.Gid);
				if (this.gid == store.state.Gid && this.list.length > 0) {
					return;
				}
				this.currentTabbarIndex = 0;
				this.gid = store.state.Gid;
				this.gname = store.state.Gname;
				this.getIndustryList();
				this.getAssociationIndex();
				const key = uni.getSystemInfoSync();
				const kk = uni.getWindowInfo();
				console.log(kk);
				var c = this.sizeDeal(120);
				var s = this.sizeDeal(140);
				this.my_page = parseInt(key.windowHeight) - parseInt(store.state.vuex_custom_bar_height) - parseInt(s) -
					parseInt(c) - parseInt(key.safeAreaInsets.bottom);
				console.log(parseInt(key.windowHeight));
					console.log(parseInt(store.state.vuex_custom_bar_height));
					console.log(parseInt(s));
					console.log(parseInt(c));
					console.log(parseInt(c));
					
			},
			confirm(d) {
				var info = d[0];
				this.list = [];
				this.gid = info.value;
				this.getAssociationIndex();
				this.getIndustryList();
			},
			getAssociationIndex() {
				associationIndex()
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.association_name
							}));
							const foundNumber = transformedSelectList.find((element) => element.value == this.gid);
							this.HomeTitle = foundNumber.label;
							this.selectList = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getIndustryList() {
				newIndustryIndex({
						association_id: this.gid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.industry_list = res.data;
							// this.industry_list.push({
							// 	id: -1,
							// 	industry_name: '其他行业'
							// });
							// this.industry_list.unshift({
							// 	id: 0,
							// 	industry_name: '组织架构'
							// });
							this.leftId = res.data[0].id;
							uni.showLoading({
								title: '加载中...'
							});
							this.getAddressList();
						} else {
							this.industry_list = [];
							this.list = [];
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getAddressList() {
				addressList({
						position_id: 0,
						association_id: this.gid,
						industry_id: this.leftId,
						// industry_id: 1,
						nikename: this.serach_content
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list = res.data.ret;
						}
						setTimeout(function() {
							uni.hideLoading();
						}, 2000);
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			clickClassifyNav(index) {
				uni.showLoading({
					title: '加载中...'
				});
				this.currentTabbarIndex = index;
				this.leftId = this.industry_list[index].id;
				this.list = [];
				this.top = this.randomBetween();
				this.getAddressList();
				//this.handleLeftScrollView(index)
				//this.switchClassifyContent();
			},
			randomBetween() {
				let baseRandom = Math.random() * (0.99999 - 0.00001) + 0.00001;
				return baseRandom.toFixed(5); //
			},
			// 点击分类后，处理scrollView滚动到居中位置
			handleLeftScrollView(index) {
				const tabbarItemTop = this.tabbarItemInfo[index].top - this.scrollViewBasicTop
				if (tabbarItemTop > this.scrollViewHeight / 2) {
					this.leftScrollViewTop = tabbarItemTop - (this.scrollViewHeight / 2) + this.tabbarItemInfo[index]
						.height
				} else {
					this.leftScrollViewTop = 0
				}
			},
			// 切换对应分类的数据
			switchClassifyContent() {
				this.rightScrollViewTop = 1
				this.$nextTick(() => {
					this.rightScrollViewTop = 0
				})
				//this.classifyContent.subClassify[0].title = this.tabbar[this.currentTabbarIndex]
			},
			tn(url) {
				uni.navigateTo({
					url: url
				})
			},
			sizeDeal(size) {

				// 使用uni.upx2px进行转换
				const pxValue = uni.upx2px(size);

				return pxValue;
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>


<style lang="scss" scoped>
	/* 自定义导航栏内容 start */
	.custom-nav {
		height: 100%;

		&__back {
			margin: auto 30rpx;
			margin-right: 10rpx;
			flex-basis: 5%;
			width: 100rpx;
			position: absolute;
		}
	}


	.left-width {
		flex-basis: 28%;
	}

	/* 自定义导航栏内容 end */
	.tn-classify {

		/* 搜索栏 start */


		/* 搜索栏 end */

		/* 分类列表和内容 strat */
		&__container {}

		&__left-box {}

		&__right-box {
			background-color: #FFFFFF;
		}

		/* 分类列表和内容 end */

		/* 侧边导航 start */
		&__tabbar {
			&__item {
				height: 90rpx;

				&:first-child {
					border-top-right-radius: 0rpx;
				}

				&:last-child {
					border-bottom-right-radius: 0rpx;
				}

				&--active {
					background-color: #FFFFFF;
					position: relative;
					// font-weight: bold;
					color: #4AA2EF;

					&--prev {
						border-bottom-right-radius: 26rpx;
					}

					&--next {
						border-top-right-radius: 26rpx;
					}
				}
			}
		}

		/* 侧边导航 end */

		/* 分类内容 start */
		&__content {
			margin: 18rpx;

			/* 推荐商品 start */
			&__recomm {
				margin-bottom: 40rpx;

				&__swiper {}
			}

			/* 推荐商品 end */

			/* 子栏目 start */
			&__sub-classify {
				margin-bottom: 20rpx;
				padding-bottom: 40rpx;

				&--title {
					font-weight: bold;
					margin-bottom: 18rpx;

				}

				&__content {

					&__item {
						padding: 20rpx;
					}
				}
			}

			/* 子栏目 end */
		}

		/* 分类内容 end */
	}
</style>