<template>
	<view style="background-color: #EBF4F7;letter-spacing: 1rpx;min-height: 100vh;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">

					<tn-tabs :list="[{name:'协会活动'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-margin">
				<view class="justify-content-item align-content-item" style="width: 100%;">
					<view class="tn-flex tn-flex-col-center"
						style="border-radius: 100rpx;padding: 10rpx 20rpx 10rpx 20rpx;width: 95%;background-color: rgba(255, 255, 255, 0.9);">
						<text
							class="tn-icon-search justify-content-item tn-padding-right-xs tn-color-gray tn-text-lg"></text>
						<input v-model="content" class="justify-content-item" placeholder="请填写活动标题" name="input"
							placeholder-style="color:#AAAAAA" style="width: 90%;"></input>
					</view>
				</view>

				<view>
					<view class="justify-content-item tn-text-center">
						<tn-button backgroundColor="#3668fc" shape="round" padding="20rpx 20rpx" width="150rpx"
							@click="onsubmit()">
							<text class="tn-color-white">搜 索</text>
						</tn-button>

					</view>
				</view>
			</view>
			<tn-tabs :list="tabList" :isScroll="false" :current="current" name="name" @change="tabChange"
				activeColor="#3668FC"></tn-tabs>

		</view>

		<view style="padding-bottom: 30rpx;">

			<view style="padding: 30rpx 0rpx;">
				<tn-grid align="left" :col="2">
					<block v-for="(item,index) in actList">
						<tn-grid-item style="width:50%">
							<view @click="openUrl('/pages/packageB/event/event_info?id='+item.id)"
								style="margin-bottom: 30rpx;background-color: #FFF;box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.1);position: relative;display: inline-block;width: 350rpx;text-align: center;border-radius: 20rpx;overflow: hidden;">
								<view>
									<image :src="apiImgUrl+item.activity_image" mode="aspectFill"
										style="width: 350rpx;height: 170rpx;">
									</image>
								</view>
								<view style="padding:10rpx 20rpx;font-weight: 400;">
									<view class="tn-text-ellipsis " style="text-align: left;">{{ item.activity_name }}
									</view>
									<view class="tn-flex tn-flex-row-between"
										style="font-size: 24rpx;color:#808080;padding: 15rpx 0rpx;">
										<view>
											<text v-if="item.activity_type==1">协会活动</text>
											<text v-if="item.activity_type==2">调查问卷</text>
											<text v-if="item.activity_type==3">公益捐赠</text>
											<text v-if="item.activity_type==4">学习培训</text>
										</view>

									</view>
								</view>

							</view>
						</tn-grid-item>
					</block>
				</tn-grid>
			</view>
		</view>
		<view style="padding-bottom: 120rpx;">
			<tn-load-more class="tn-margin-top" :status="load_status"></tn-load-more>
		</view>
		<tn-tabbar :outHeight="140" :height="120" v-model="currentIndex" :list="tabbarList" activeColor="#3377FF"
			inactiveColor="#AAAAAA" activeIconColor="#3377FF" inactiveIconColor="#8A8E99" :animation="true"
			:safeAreaInsetBottom="true" @change="switchTabbar"></tn-tabbar>
	</view>
</template>

<script>
	import {
		activityIndex,
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		data() {
			//1.线下活动2.调查问卷3.公益捐赠4.学习培训
			return {
				currentIndex: -1,
				// 底部tabbar菜单数据
				tabbarList: [{
						title: '首页',
						activeIcon: '/static/01_1.png',
						inactiveIcon: '/static/01.png'
					},
					{
						title: '通讯录',
						activeIcon: '/static/02_2.png',
						inactiveIcon: '/static/02.png'
					},
					{
						title: '发现',
						activeIcon: '/static/03_3.png',
						inactiveIcon: '/static/03.png'
					},
					{
						title: '个人中心',
						activeIcon: '/static/04_4.png',
						inactiveIcon: '/static/04.png'
					}
				],
				current: 0,
				load_status: 'loading ',
				tabList: [{
					name: '全部',
					id: 0
				}, {
					name: '协会活动',
					id: 1
				}, {
					name: '调查问卷',
					id: 2
				}, {
					name: '公益捐赠',
					id: 3
				}, {
					name: '学习培训',
					id: 4
				}],
				apiImgUrl: this.$store.state.imgUrl,
				content: '',
				topCurrent: 0,
				actList: [],
				page: 1,
			}
		},
		onLoad(d) {
			console.log(d);
			if (typeof(d.type) != 'undefined') {
				this.current = d.type;
			}
			this.getActivityIndex();
		},
		methods: {
			switchTabbar(d) {
				console.log(d);
				uni.$emit('depId', {
					index: d
				})
				uni.navigateBack()
			},
			onsubmit() {
				this.page = 1;
				this.actList = [];
				this.getActivityIndex();
			},
			tabChange(d) {
				console.log(d);
				this.current = d;
				this.page = 1;
				this.actList = [];
				this.getActivityIndex();
			},
			ReachBottom() {
				console.log('service');
				this.page = this.page + 1;
				this.getActivityIndex();
				//this.getIndex();
			},
			getNewInfo() {
				this.current = 0;
				this.page = 1;
				this.actList = [];
				this.getActivityIndex();
			},
			getActivityIndex() {
				var type = this.tabList[this.current].id;
				activityIndex({
						association_id: store.state.Gid,
						page: this.page,
						size: 10,
						activity_name: this.content,
						activity_type: type
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.actList.push(...res.data.ret);
						} else {
							this.load_status = 'nomore';
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
			openUrl(e) {
				uni.navigateTo({
					url: e
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>
