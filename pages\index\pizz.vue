<template>
	<!-- 发现 -->
	<view style="background-color: #EBF4F7;letter-spacing: 1rpx;min-height: 100vh;">
		<tn-nav-bar :isBack="false" :bottomShadow="false" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
				<view class="tn-margin-top" style="text-align: center;">
					<text>发现</text>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}"
			style="background-color: #ffffff;padding-bottom: 10rpx;">
			<view style="padding:0rpx 20rpx">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-margin"
					style="background-color: #ffffff;">
					<view class="justify-content-item align-content-item" style="width: 100%;">
						<view class="tn-flex tn-flex-col-center"
							style="border-radius: 100rpx;padding: 15rpx 20rpx;width: 100%;background-color:#F1F2F8;">
							<text
								class="tn-icon-search justify-content-item tn-padding-right-xs tn-color-gray tn-text-lg"></text>
							<input @confirm="onsubmit()" confirm-type="search" v-model="content"
								class="justify-content-item" placeholder="请输入协会名称进行搜索" name="input"
								placeholder-style="color:#AAAAAA" style="width: 90%;"></input>
						</view>
					</view>

					<!-- <view>
						<view class="justify-content-item tn-text-center">
							<tn-button backgroundColor="#3668fc" shape="round" padding="20rpx 20rpx" width="150rpx"
								@click="onsubmit()">
								<text class="tn-color-white">搜 索</text>
							</tn-button>
						</view>
					</view> -->
				</view>
			</view>

			<!--			<view class="tn-flex tn-flex-row-around tn-flex-center tn-flex-col-center" style="padding: 30rpx;">-->
			<!--				<view style="color: #E15033;">-->
			<!--					<text class="tn-icon-sequence-vertical" style="vertical-align: middle;"></text>-->
			<!--					<text style="margin-left: 15rpx;vertical-align: middle;">全部排序</text>-->
			<!--				</view>-->
			<!--				<view style="height: 25rpx;width: 2rpx;background-color: #808080;"></view>-->
			<!--				<view>-->
			<!--					<text class="tn-icon-first" style="vertical-align: middle;"></text>-->
			<!--					<text style="margin-left: 15rpx;vertical-align: middle;">人气榜</text>-->
			<!--				</view>-->
			<!--				<view style="height: 25rpx;width: 2rpx;background-color: #808080;"></view>-->
			<!--				<view>-->
			<!--					<text class="tn-icon-light" style="vertical-align: middle;"></text>-->
			<!--					<text style="margin-left: 15rpx;vertical-align: middle;">最新入驻</text>-->
			<!--				</view>-->
			<!--			</view>-->
		</view>
		<!--		<view class="tn-flex tn-flex-row-between tn-flex-center tn-flex-col-center" style="padding: 30rpx;">-->
		<!--			<view style="text-align: center;background-color: #FFF;padding:10rpx 30rpx;width: 48%;border-radius: 5rpx;">-->
		<!--				<text>省协会</text>-->
		<!--				<text class="tn-icon-down-triangle"></text>-->
		<!--			</view>-->
		<!--			<view style="text-align: center;background-color: #FFF;padding:10rpx 30rpx;width: 48%;border-radius: 5rpx;">-->
		<!--				<text>市协会</text>-->
		<!--				<text class="tn-icon-down-triangle"></text>-->
		<!--			</view>-->
		<!--		</view>-->
		<view style="padding:30rpx 30rpx 180rpx 30rpx;">
			<view style="position: relative;" @click="switchTabbar(1)">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="box-shadow: 0rpx 4rpx 25rpx 0rpx rgba(5,171,129,0.25);background: linear-gradient(to bottom,rgba(19,194,150,0.7) 0%, #13C296 100%);padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 20rpx;">
					<view>
						<image :src="apiImgUrl+'/uploads/1/20240628/a78696e22b3cecbf678afbbcca617fca.png'"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="color:#ffffff;font-size: 32rpx;font-weight: 600;">洛阳市青年户外协会
						</view>
						<view style="font-size: 28rpx;color: rgba(255,255,255,0.6);margin-top: 15rpx;">成立时间：2024-8-13
						</view>
					</view>
				</view>

				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
				<view class="triangle"></view>
			</view>
			
			<view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between"
				style="color: #CCCCCC;margin: 50rpx 0rpx 40rpx 0rpx;">
				<view style="border: 1rpx dashed #CCCCCC;width: 100%;"></view>
				<!-- <view style="width: 500rpx;text-align: center;font-size: 24rpx;">分协会展示</view> -->
				<view style="border: 1rpx dashed #CCCCCC;width: 100%;"></view>
			</view>
			
			
			
			<view style="position: relative;" @click="handleRemind()">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image :lazy-load="true" src="https://ysx.0rui.cn/h5/static/lunhua.jpg" mode="aspectFill"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="font-size: 32rpx;font-weight: 600;">洛阳市轮滑协会
						</view>
						<view style="font-size: 28rpx;color: #888888;margin-top: 15rpx;">成立时间：2014年12月5日</view>
					</view>
				</view>
				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
			</view>
			<view style="position: relative;" @click="handleRemind()">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image :lazy-load="true" src="https://ysx.0rui.cn/h5/static/zixingche.jpg" mode="aspectFill"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="font-size: 32rpx;font-weight: 600;">洛阳市自行车协会
						</view>
						<view style="font-size: 28rpx;color: #888888;margin-top: 15rpx;">成立时间：2012年12月14日</view>
					</view>
				</view>
				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
			</view>
			<view style="position: relative;" @click="handleRemind()">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image :lazy-load="true" src="@/static/lunhua.jpg" mode="aspectFill"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="font-size: 32rpx;font-weight: 600;">洛阳市滑板协会
						</view>
						<view style="font-size: 28rpx;color: #888888;margin-top: 15rpx;">成立时间：2014年12月5日</view>
					</view>
				</view>
				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
			</view>
			<view style="position: relative;" @click="handleRemind()">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image :lazy-load="true" src="@/static/lunhua.jpg" mode="aspectFill"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="font-size: 32rpx;font-weight: 600;">洛阳市摩托车协会
						</view>
						<view style="font-size: 28rpx;color: #888888;margin-top: 15rpx;">成立时间：2024年12月5日</view>
					</view>
				</view>
				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
			</view>
			<view style="position: relative;" @click="handleRemind()">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image :lazy-load="true" src="@/static/lunhua.jpg" mode="aspectFill"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="font-size: 32rpx;font-weight: 600;">洛阳市高尔夫协会
						</view>
						<view style="font-size: 28rpx;color: #888888;margin-top: 15rpx;">成立时间：2018年12月5日</view>
					</view>
				</view>
				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
			</view>
			
			
			<!-- <view v-for="(item,index) in list" style="position: relative;" @click="switchTabbar(item.id)">
				<view class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:40rpx 30rpx;border-radius: 30rpx;margin-bottom: 30rpx;">
					<view>
						<image :lazy-load="true" :src="apiImgUrl+item.association_image"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 30rpx;">
						<view style="font-size: 32rpx;font-weight: 600;">{{item.association_name}}
						</view>
						<view style="font-size: 28rpx;color: #888888;margin-top: 15rpx;">成立时间：{{item.createtime}}</view>
					</view>
				</view>
				<view
					style="font-size: 20rpx;color: #09AE85;background-color: #B8EDE0;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 15rpx;">
					<text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
				</view>
			</view> -->
			
			
		</view>
		<!--		<view style="position: fixed;bottom: 13%;width: 100%;">-->
		<!--			<view @click="tn('/pages/index/enter_in')"-->
		<!--				style="margin: 0 auto;color: #fff;letter-spacing: 10rpx;line-height: 70rpx;;text-align: center;width: 70%;height: 70rpx;background: linear-gradient(270deg, #EE7E45, #EE9657);border-radius: 50rpx;">-->
		<!--				快速入驻</view>-->
		<!--		</view>-->
	</view>
</template>

<script>
	import {
		associationIndex
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				topCurrent: 0,
				list: [],
				apiImgUrl: this.$store.state.imgUrl,
				content: '',
				page: 1,
			}
		},
		mounted() {
			this.getAssociationIndex();
		},
		onReachBottom() {
			console.log(1);
		},
		methods: {
			handleRemind() {
				uni.showToast({
					title: '请敬请期待',
					icon:'none',
					duration: 2000 // 设置显示时长为2000毫秒
				})
			},
			switchTabbar(d) {
				console.log(d);
				if (d == 0) {
					return;
				}
				store.commit('$tStore', {
					name: 'Gid',
					value: d
				})
				uni.setStorageSync('Gid', d);
				uni.$emit('getGid', {
					gid: d
				})
				uni.$emit('depId', {
					index: 0
				})
			},
			onsubmit() {
				this.page = 1;
				this.list = [];
				this.getAssociationIndex();
			},
			ReachBottom() {
				this.page = this.page + 1;
				//this.getNewsList();
				this.getAssociationIndex();
			},
			getAssociationIndex() {
				associationIndex({
						association_name: this.content,
						page: this.page,
						size: 10,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list.push(...res.data);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			tn(e) {
				uni.navigateTo({
					url: e
				})
			}
		}
	}
</script>

<style>
	.triangle {
		width: 0;
		height: 0;
		border-top: 20rpx solid #13C296;
		border-right: 20rpx solid transparent;
		border-left: 20rpx solid transparent;
		position: absolute;
		left: 0;
		right: 0;
		margin: 0 auto;
		bottom: -12rpx;
		z-index: 100;
	}
</style>