<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'搜索'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view>
			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-margin"
				:style="{marginTop: vuex_custom_bar_height + 20+'px'}">
				<view class="justify-content-item align-content-item" style="width: 100%;">
					<view class="tn-flex tn-flex-col-center"
						style="border-radius: 100rpx;padding: 10rpx 20rpx 10rpx 20rpx;width: 95%;background-color: rgba(248, 247, 248, 0.9);">
						<text
							class="tn-icon-search justify-content-item tn-padding-right-xs tn-color-gray tn-text-lg"></text>
						<input v-model="content" class="justify-content-item" placeholder="请填写搜索内容" name="input"
							placeholder-style="color:#AAAAAA" style="width: 90%;"></input>
					</view>
				</view>

				<view class="align-content-item">
					<view class="justify-content-item tn-text-center">
						<tn-button backgroundColor="#3668fc" shape="round" padding="20rpx 20rpx" width="150rpx"
							@tap="getSerach">
							<text class="tn-color-white">搜 索</text>
						</tn-button>

					</view>
				</view>
			</view>
		</view>
		<view v-if="is_serach">
			<!-- <block>
				<view>
					<view class="tn-flex tn-flex-row-between tn-margin">
						<view class="justify-content-item tn-text-bold">
							<text class="tn-text-df tn-color-black">最近搜索</text>
						</view>
						<view class="justify-content-item tn-text-df tn-color-gray">
							<text class="tn-padding-xs">删除</text>
							<text class="tn-icon-delete"></text>
						</view>
					</view>
				</view>
				<view class="">
					<view class="tn-tag-search  tn-text-justify" style="padding: 0rpx 30rpx;">
						<view v-for="(item, index) in tagList" :key="index"
							class="tn-tag-search__item tn-margin-right tn-round tn-text-sm tn-bg-gray--light tn-color-gray">
							{{ item.name }}
						</view>
					</view>
				</view>
			</block> -->
			<view v-if="list.member.length>0" class="tn-flex tn-flex-row-between tn-padding-bottom"
				style="padding-top: 20rpx;margin: 30rpx 30rpx 0rpx 30rpx;">
				<view class="justify-content-item tn-text-bold">
					<text class="tn-text-df tn-color-black">会员搜索结果</text>
				</view>
			</view>
			<view style="padding:0rpx 30rpx;">
				<view v-for="(item,index) in list.member" class="tn-flex tn-flex-center tn-flex-col-center"
					@click="tn('/pages/packageA/user/my_card?id='+item.id)"
					style="background-color: #ffffff;padding:30rpx;border-radius: 15rpx;margin-bottom: 20rpx;box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.1);">
					<view>
						<image v-if="item.photo_image!=null&&item.photo_image!=''" :src="apiImgUrl+item.photo_image"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
						<image v-if="item.photo_image==null||item.photo_image==''" src="/static/def.png"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 20rpx;">
						<view style="font-size: 31rpx;font-weight: 600;letter-spacing: 5rpx;">{{item.nikename}}</view>
						<view style="font-size: 24rpx;colo r: #808080;margin-top: 15rpx;">{{item.enterprise_name}}
						</view>
					</view>
				</view>
			</view>
			<view v-if="list.association.length>0" class="tn-flex tn-flex-row-between tn-padding-bottom"
				style="padding-top: 20rpx;margin: 30rpx 30rpx 0rpx 30rpx;">
				<view class="justify-content-item tn-text-bold">
					<text class="tn-text-df tn-color-black">协会搜索结果</text>
				</view>
			</view>
			<view style="padding:0rpx 30rpx;">
				<view @click="tn('/pages/index/pizz_info?id='+item.id)" v-for="(item,index) in list.association"
					class="tn-flex tn-flex-center tn-flex-col-center"
					style="background-color: #ffffff;padding:30rpx;border-radius: 15rpx;margin-bottom: 20rpx;box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.1);">
					<view>
						<image :src="apiImgUrl+item.association_image"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
					</view>
					<view style="margin-left: 20rpx;">
						<view style="font-size: 31rpx;font-weight: 600;letter-spacing: 5rpx;">{{item.association_name}}
						</view>
						<view style="font-size: 24rpx;color: #808080;margin-top: 15rpx;">成立时间：{{item.createtime}}</view>
					</view>
				</view>
			</view>
			<view v-if="list.activity.length>0" class="tn-flex tn-flex-row-between tn-padding-bottom"
				style="padding-top: 20rpx;margin: 30rpx 30rpx 0rpx 30rpx;">
				<view class="justify-content-item tn-text-bold">
					<text class="tn-text-df tn-color-black">活动搜索结果</text>
				</view>
			</view>
			<view>
				<view v-for="(item,index) in list.activity" style="background-color: #ffffff;">
					<view class="tn-flex tn-flex-row-between"
						@click="tn('/pages/packageB/event/event_info?id='+item.id)" style="padding: 20rpx;">
						<view style="margin-left: 20rpx;" v-if="item.activity_image">
							<image :src="apiImgUrl+item.activity_image" mode="aspectFill"
								style="width: 230rpx;height: 180rpx;border-radius: 8rpx;"></image>
						</view>
						<view style="position:relative;min-height: 200rpx;width: 100%;">
							<view class="tn-text-ellipsis-3" style="font-size: 28rpx;padding-left: 20rpx;">
								{{ item.activity_name }}
							</view>
							<view class="tn-flex tn-flex-row-between"
								style="width: 100%;padding: 20rpx;font-size:24rpx;color: rgb(171, 171, 171);position: absolute; bottom: 0rpx;overflow: hidden">
								<view>{{formatTime(item.createtime) }}</view>
								<view>
									<text v-if="item.activity_type==1" style="vertical-align: middle;">协会活动</text>
									<text v-if="item.activity_type==2" style="vertical-align: middle;">调查问卷</text>
									<text v-if="item.activity_type==3" style="vertical-align: middle;">公益捐赠</text>
									<text v-if="item.activity_type==4" style="vertical-align: middle;">学习培训</text>
								</view>
							</view>
						</view>
					</view>
					<view v-if="list.news.length-1>index"
						style="background: rgba(217, 217, 217, 0.5);width: 90%;margin: 0 auto;height: 2rpx;"></view>
				</view>
			</view>
			<view v-if="list.news.length>0" class="tn-flex tn-flex-row-between tn-padding-bottom"
				style="padding-top: 20rpx;margin: 30rpx 30rpx 0rpx 30rpx;">
				<view class="justify-content-item tn-text-bold">
					<text class="tn-text-df tn-color-black">新闻搜索结果</text>
				</view>
			</view>
			<view style="padding:0px 30rpx;">
				<view v-for="(item,index) in list.news" style="background-color: #ffffff;">
					<view class="tn-flex tn-flex-row-between" @click="tn('/pages/packageB/news/new_info?id='+item.id)"
						style="padding: 20rpx;">
						<view style="margin-left: 20rpx;" v-if="item.news_image">
							<image :src="apiImgUrl+item.news_image" mode="aspectFill"
								style="width: 230rpx;height: 180rpx;border-radius: 8rpx;"></image>
						</view>
						<view style="position:relative;min-height: 200rpx;width: 100%;">
							<view class="tn-text-ellipsis-3" style="font-size: 28rpx;padding-left: 20rpx;">
								{{ item.news_title }}
							</view>
							<view class="tn-flex tn-flex-row-between"
								style="width: 100%;padding: 20rpx;font-size:24rpx;color: rgb(171, 171, 171);position: absolute; bottom: 0rpx;overflow: hidden">
								<view>{{ formatTime(item.showtime)}}</view>
								<view>
									<text class="tn-icon-eye" style="vertical-align: middle;"></text>
									<text style="vertical-align: middle;">{{ item.news_hits }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-if="list.news.length-1>index"
						style="background: rgba(217, 217, 217, 0.5);width: 90%;margin: 0 auto;height: 2rpx;"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getSerachIndex
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				content: '',
				topCurrent: 0,
				// tagList: [{
				// 	name: '李小兵'
				// }, {
				// 	name: '李小兵'
				// }],
				list: {
					activity: [],
					association: [],
					member: [],
					news: [],
				},
				is_serach: false,
				apiImgUrl: this.$store.state.imgUrl,
				association_id: this.$store.state.Gid,
			}
		},
		methods: {
			formatTime(time) {
				const [year, month, day] = time.split(' ')[0].split('-');
				return `${year}-${month}-${day}`;
			},
			getSerach() {

				getSerachIndex({
						name: this.content
					})
					.then(res => {
						console.log(res);
						this.list = res.data;
						this.is_serach = true;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			tn(e) {
				uni.navigateTo({
					url: e
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.tn-search-fixed {
		position: fixed;
		top: 50rpx;
		width: 100%;
		transition: all 0.25s ease-out;
		z-index: 1;
	}


	/* 搜索标签 start*/
	.tn-tag-search {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 20rpx 20rpx 5rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/

	/* 标题 start */
	.nav_title {
		-webkit-background-clip: text;
		color: transparent;

		&--wrap {
			position: relative;
			display: flex;
			height: 120rpx;
			font-size: 42rpx;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			background-image: url(https://resource.tuniaokj.com/images/title_bg/title00.png);
			background-size: cover;
		}
	}

	/* 标题 end */

	/* 富文本图示意 start */
	.news-img {
		z-index: -1;
		padding-bottom: 40rpx;

		image {
			width: 100%;
			margin: 20rpx 0;
			// height: 3373rpx;
			// z-index: -1;
		}
	}

	/* 资讯主图 start*/
	.image-article {
		border-radius: 8rpx;
		border: 1rpx solid #F8F7F8;
		width: 200rpx;
		height: 200rpx;
		position: relative;
	}

	.image-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 10rpx;
	}

	.article-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}

	/* 文字截取*/
	.clamp-text-1 {
		-webkit-line-clamp: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.clamp-text-2 {
		-webkit-line-clamp: 2;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 35rpx;
			padding: 7rpx 25rpx 5rpx 25rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/
</style>