<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view class="tn-margin-top"
					style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:info.association_name}]" :current="topCurrent" activeColor="#000"
						:bold="false" :fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="position: relative;">
				<swiper class="card-swiper" :circular="true" :autoplay="true" duration="500" interval="8000"
					@change="cardSwiper" style="height: 370rpx;">
					<swiper-item style="padding: 0px;border-radius: 0;">
						<view class="swiper-item image-banner"
							:style="'background-image:url('+ apiImgUrl+info.association_image + ');background-size:auto 100% ; background-position:center;  background-repeat: no-repeat;border-radius: 0;'">
						</view>
					</swiper-item>
				</swiper>
				<!--        <view style="position: absolute;top: 10px;right: 10px;">-->
				<!--          <view-->
				<!--              style="position: relative;;width: 70rpx;height: 60rpx;background-color: rgba(255, 255, 255, 0.9);border-radius: 8rpx;box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.2);">-->
				<!--            <image src="/static/c1455.png" mode="widthFix"-->
				<!--                   style="width: 40rpx;position: absolute;left: 0;right: 0;margin: 0 auto;top: 15rpx;"></image>-->
				<!--          </view>-->
				<!--        </view>-->
			</view>
		</view>
		<view style="padding:20rpx 30rpx;">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between" style="font-size: 30rpx;">
				<view>协会地址</view>
				<view>{{info.association_dizhi}}</view>
			</view>
			<view style="margin: 20rpx 0rpx;font-size: 30rpx;"
				class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between">
				<view>协会邮箱</view>
				<view>{{info.association_emal}}</view>
			</view>
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-between" style="font-size: 30rpx;">
				<view>联系方式</view>
				<view>{{info.association_phone}}</view>
			</view>
		</view>
		<view style="height: 10rpx;background-color: #EBF4F7;"></view>
		<view style="padding:10rpx 30rpx 200rpx 30rpx;">
			<tn-tabs :list="list" :isScroll="false" :activeItemStyle="{'fontWeight':'600','fontSize':'30rpx'}"
				style="font-weight: ;" activeColor="#000" :barWidth="50" :barHeight="6"
				:barStyle="{'background': 'linear-gradient(-45deg, #4AA2EF, #3A7FF4)','borderRadius': '4rpx'}"
				:current="current" name="name" @change="change"></tn-tabs>
			<!--			<view style="margin-top: 20rpx;position: relative;">-->
			<!--				<image src="/static/b1.png"-->
			<!--					style="width: 100rpx;height: 100rpx;position: absolute;left: 0;right: 0;top: 38%;z-index: 10;margin: 0 auto;">-->
			<!--				</image>-->
			<!--				<image src="/static/t7.jpg" style="width: 100%;border-radius: 20rpx;" mode="widthFix"></image>-->
			<!--			</view>-->
			<view style="line-height: 50rpx; text-indent: 2em;margin-top: 20rpx;" v-if="current==0">
				<div v-html="info.association_desc"></div>
			</view>
			<view style="line-height: 50rpx; text-indent: 2em;margin-top: 20rpx;" v-if="current==2">
				<div v-html="info.association_guizhang"></div>
			</view>
			<view style="line-height: 50rpx; text-indent: 2em;margin-top: 20rpx;" v-if="current==3">
				<div v-html="info.ruhuixizhu"></div>
			</view>
			<view style="padding: 30rpx;text-align: center; " v-if="current==1">
				<template v-for="(item, index) in architecture">
					<tn-button backgroundColor="#E83A30" height="50rpx" width="250rpx" fontColor="#ffffff">
						{{item.position_name}}
					</tn-button>
					<tn-grid align="center" col="5">
						<tn-grid-item v-if="item.neirong" style="width:20%" v-for="(v,i) in item.neirong">
							<view style="padding: 30rpx;">
								<image :src="apiImgUrl+v.photo_image"
									style="width: 100rpx;height: 100rpx;border-radius: 50%;">
								</image>
								<view>{{v.nikename}}</view>
							</view>
						</tn-grid-item>
						<tn-grid-item v-if="!item.neirong" style="width:20%">
							<view style="padding: 30rpx;">
								<view>暂无</view>
							</view>
						</tn-grid-item>
					</tn-grid>
				</template>
			</view>
		</view>
		<view v-if="userInfo.association_id!=info.id" @click="openUrl('/pages/packageA/user/apply_in')"
			style="position: fixed;bottom: 330rpx;right: 20rpx;line-height: 35rpx;color: #fff;;text-align: center;width: 100rpx;height: 100rpx;background: linear-gradient(270deg, #EE7E45, #EE9657);border-radius: 50%;">
			<view style="padding-top: 16rpx;">申请</view>
			<view>入会</view>
		</view>
		<view style="position: fixed;bottom: 310rpx; width: 100%;">
			<view class="tn-flex tn-flex-row-around">
				<view @click="openUrl('/pages/packageA/info/directory?id='+info.id)"
					style="position: absolute; right:20rpx;color: #fff;  line-height:35rpx;text-align: center;width: 100rpx;height: 100rpx;background: linear-gradient(-45deg, #4AA2EF, #3A7FF4);border-radius: 50%;">
					<view style="padding-top: 16rpx;">会员</view>
					<view>名录</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		associationInfo,
		architectureList,
		getUserIndex,
	} from '@/util/api.js';
	import store from "@/store";

	export default {
		data() {
			return {
				topCurrent: 0,
				swiperList: [{
					url: '/static/banner1.jpg',
				}],
				list: [{
					name: '协会简介'
				}, {
					name: '组织架构'
				}, {
					name: '规章制度',
				}, {
					name: '入会须知',
				}],
				current: 0,
				cardCur: 0,
				info: {},
				apiImgUrl: this.$store.state.imgUrl,
				architecture: [],
				userInfo: {}
			}
		},
		methods: {
			getUserInfo() {
				var uid = uni.getStorageSync('uid');
				if (uid) {
					getUserIndex({
							member_id: uid
						})
						.then(res => {
							console.log(res);
							if (res.code == 1) {
								this.userInfo = res.data;
							}
						})
						.catch(error => {
							uni.showToast({
								title: error,
								icon: 'none',
								duration: 2000
							});
						})
				}

			},
			getArchitectureList() {
				architectureList({
						association_id: store.state.Gid,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.architecture = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getAssociationInfo() {
				console.log('discovery');
				associationInfo({
						association_id: store.state.Gid,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							if (key.association_desc.indexOf("<img") != -1) {
								key.association_desc = getApp().addWidthToImages(key.association_desc);
							}
							this.info = key;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getNewInfo() {
				this.getAssociationInfo();
				this.getArchitectureList();
				this.getUserInfo();
			},
			openUrl(url) {
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					uni.showToast({
						title: '请登录！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				uni.navigateTo({
					url: url
				})
			},
			openPages(url) {
				uni.navigateTo({
					url: url
				})
			},
			change(e) {
				this.current = e;
			},
		}
	}
</script>

<style>
	.tn-flex-row-around {
		position: relative;

	}
</style>
