// 引入 jweixin-module
const jweixin = require('jweixin-module');

// 通过export 暴露常用的方法
export default {
	//判断是否在微信中    
	isWechat: function() {
		var ua = window.navigator.userAgent.toLowerCase();
		if (ua.match(/micromessenger/i) == 'micromessenger') {
			//console.log('是微信客户端')  
			return true;
		} else {
			//console.log('不是微信客户端')  
			return false;
		}
	},

	// 初始化WXSDK
	initJssdk: async function(callback) {
		/* ****************************************
		 * 获取当前url然后传递给后台获取授权和签名信息
		 # TODO: 后台返回签名
		 *************************************** */
		// let uri = encodeURIComponent(window.location.href.split('#')[0]);
		let uri = window.location.href.split('#')[0];
		console.log(uri); // 这
		// 获取后台返回的签名
		// 这里需要换成开发者自己的接口。
		// 此处我对uni.request进行了封装，
		// 直接采用uni.request方法然后在成功的回调里面完成SDK初始化也是可行的，根据实际情况处理
		let res = await this.GetSignature(uri);
		console.log(res); // 这里输出你的签名结果
		//注入config权限配置  
		let wxConf = {
			debug: true, // 是否开启调试，第一次尝试，建议开启，后续可关闭
			appId: res.data.appId, // 必填，公众号的唯一标识
			timestamp: res.data.timestamp, // 必填，生成签名的时间戳
			nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
			signature: res.data.signature, // 必填，签名
			jsApiList: [ //这里是需要用到的接口名称  
				'updateAppMessageShareData', //分享接口  
				'updateTimelineShareData'
			]
		};
		jweixin.config(wxConf); //完成微信配置
	},
	async GetSignature(uri) {
		try {
			const response = await uni.request({
				// url: 'http://ysh.0rui.cn/index.php',
				url: 'https://ysx.0rui.cn/index.php',
				data: {
					url: uri
				},
				method: 'POST',
				header: {
					'content-type': 'application/x-www-form-urlencoded',
				}
			});
			console.log(response); // 这里输出你的签名结果
			// 在这里可以添加你的其他逻辑，处理获取到的签名数据
			return response[1]; // 如果需要在函数外部使用签名数据，可以将其返回
		} catch (error) {
			console.error('Error in GetSignature:', error);
			// 在这里处理错误情况，或者抛出异常
			throw error;
		}
	},
	/* ***************************************
	 *
	 *   微信SDK功能实例：调用微信扫码
	 *  若没有初始化需要先调用initJssdk完成初始化
	 *  若当前页面已经完成initJssdk初始化，调用扫码功能是可以直接调用 jweixin.scanQRCode
	 * 
	 * **************************************/
	scanCode: function(callback, needResult = 1) {
		if (!this.isWechat()) {
			console.log('不是微信客户端')
			return;
		}
		this.initJssdk(function(res) {
			jweixin.ready(function() {
				jweixin.scanQRCode({
					needResult: needResult, // 0扫码结果由微信处理， 1是直接返回结果，由开发者处理
					scanType: ["qrCode", "barCode"],
					success: function(rs) {
						console.log(rs);
						callback(rs)
					}
				})
			});
		});
	}
}