<template>
	<view>
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'活动签到'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 20+'px'}" v-if="info!=''">
			<view style="padding:0rpx 30rpx;text-align: center;">
				<view style="">
					<text class="tn-icon-trust-fill" style="font-size: 250rpx;color: #01BEFF;"></text>
				</view>
				<view style="font-size: 38rpx;font-weight: 600;margin-top: 40rpx;">{{info.activity_name}}</view>
				<view style="margin-top: 100rpx;font-size: 32rpx;font-weight: 600;">
					<view>开始时间：{{info.activity_start_time}}</view>
					<view style="margin: 20rpx 0rpx;">-</view>
					<view>结束时间：{{info.activity_end_time}}</view>
				</view>
				<view style="margin-top: 100rpx;">
					<tn-button @click="sign()" backgroundColor="#01BEFF" :shadow="true" fontColor="#ffffff"
						width="200rpx" height="200rpx" :fontSize="40" shape="icon" margin="10rpx 10rpx">签到</tn-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		activitySign,
		eventInfo
	} from '@/util/api.js';
	import store from '@/store/index.js'
	export default {
		data() {
			return {
				topCurrent: 1,
				activity_id: '',
				member_id: '',
				association_id: '',
				info: ''
			}
		},
		onLoad(query) {
			const q = decodeURIComponent(query.q) // 获取到二维码原始链接内容
			var data = this.getUrlParams(q);
			this.activity_id = data.id;
			this.association_id = data.association_id;
			var that = this;
			console.log('---1---');
			getApp().getUserLogin((r) => {
				console.log('---2---');
				that.member_id = r.data.id;
				that.getEventInfo();
			})
		},
		methods: {
			getEventInfo() {
				console.log('---3---');
				eventInfo({
						association_id: this.association_id,
						id: this.activity_id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.info = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			sign() {
				activitySign({
						activity_id: this.activity_id,
						member_id: this.member_id,
						association_id: this.association_id,
					})
					.then(res => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
			getUrlParams(url) {
				// \w+ 表示匹配至少一个(数字、字母及下划线), [\u4e00-\u9fa5]+ 表示匹配至少一个中文字符
				let pattern = /(\w+|[\u4e00-\u9fa5]+)=(\w+|[\u4e00-\u9fa5]+)/ig;
				let result = {};
				url.replace(pattern, ($, $1, $2) => {
					result[$1] = $2;
				})
				return result
			}
		}
	}
</script>

<style>
	page {
		background-color: #ffffff;
	}
</style>