<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:info.association_name}]" :current="topCurrent" activeColor="#000"
						:bold="false" :fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding: 30rpx;line-height: 50rpx;">
				<view style="text-align: center;font-size: 35rpx;font-weight: 600;">协会服务</view>
				<view v-html="info.association_guizhang"></view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		associationInfo,
		UserApplication
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				topCurrent: 0,
				info: {}
			}
		},
		onLoad() {
			this.getAssociationInfo();
		},
		methods: {
			getAssociationInfo() {
				console.log('discovery');
				var u = uni.getStorageSync('userInfo');
				associationInfo({
						association_id: u.association_id,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							if (key.association_guizhang.indexOf("<img") != -1) {
								key.association_guizhang = getApp().addWidthToImages(key.association_guizhang);
							}
							this.info = key;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style>

</style>