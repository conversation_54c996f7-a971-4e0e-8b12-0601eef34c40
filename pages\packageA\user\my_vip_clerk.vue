<template>
  <view class="message tn-safe-area-inset-bottom">
    <tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
      <view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
        <view style="padding-left: 15rpx;" @click="goBack()">
          <text class="tn-icon-left" style="font-size: 40rpx;"></text>
        </view>
        <view class="tn-margin-top"
              style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
          <tn-tabs :list="[{name:'店员管理'}]" activeColor="#000" :bold="false" :fontSize="36"></tn-tabs>
        </view>
      </view>
    </tn-nav-bar>
    <view class="" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
      <view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between" style="padding: 30rpx;">
        <view>当前店铺：</view>
        <view @click="select_show = true">
          <text>{{ select_name }}</text>
          <text class="tn-icon-down-triangle"></text>
        </view>
      </view>
      <tn-select v-model="select_show" mode="single" :list="select_list" @confirm="add_confirm"></tn-select>
      <view style="text-align: center;padding: 30rpx;">
        <tn-button backgroundColor="#01BEFF" @click="addClerkShow = true" fontColor="#ffffff">添加店员</tn-button>
      </view>
      <view style="margin: 20rpx;font-size: 35rpx;font-weight: 600;">在职店员</view>
      <view v-for="(item,index) in list" class="tn-flex tn-flex-center tn-flex-row-between" style="padding: 30rpx;">
        <view class="tn-flex tn-flex-center tn-flex-col-center">
          <view style="font-size: 30rpx;font-weight: 600;">{{ item.clerk_name }}</view>
          <view style="margin-left: 20rpx;" @click="open_phoen(item.phone)">{{ item.phone }}</view>
          <view style="margin-left: 20rpx;">
            <tn-tag v-if="item.if_bangding==2" size="sm" backgroundColor="#FF7043" width="80rpx" shape="circle"
                    fontColor="#ffffff">未绑定
            </tn-tag>
            <tn-tag v-if="item.if_bangding==1" size="sm" backgroundColor="#28B93D" width="80rpx" shape="circle"
                    fontColor="#ffffff">已绑定
            </tn-tag>
          </view>
        </view>
        <view>
          <tn-button @click="del(item.id)" backgroundColor="#E83A30" size="sm" shape="round" fontColor="#ffffff">删除
          </tn-button>
        </view>
      </view>
    </view>
    <tn-popup v-if="addClerkShow" :closeBtn="true" v-model="addClerkShow" mode="center" width="500rpx">
      <view style="text-align: center;padding: 30rpx 0rpx;font-size: 32rpx;font-weight: 600;">添加店员</view>
      <view style="text-align: center;width: 400rpx;margin:0rpx auto;">
        <tn-form :model="formAdd" ref="form" labelWidth="120">
          <tn-form-item label="姓名" prop="name">
            <tn-input v-model="formAdd.clerk_name" :clearable="false"/>
          </tn-form-item>
          <tn-form-item label="手机号" prop="intro">
            <tn-input v-model="formAdd.phone" :clearable="false"/>
          </tn-form-item>
        </tn-form>
      </view>
      <view style="text-align: center;margin-top: 50rpx;padding-bottom: 50rpx;">
        <tn-button @click="add()" shape="round" width="80%" backgroundColor="#3F8BF2" fontColor="#ffffff">确定
        </tn-button>
      </view>
    </tn-popup>
    <tn-modal v-model="show" :title="title" :content="content" :button="button" @click="dian"></tn-modal>
  </view>
</template>

<script>
import {
  clerkListByBusiness,
  addClerk,
  businessListByMember, deleteClerk
} from "@/util/api";
import store from "@/store";

export default {
  data() {
    return {
      show: false,
      title: '提示信息',
      content: '确定要删除店员吗？',
      button: [{
        text: '取消',
        backgroundColor: '#F4F4F4',
        fontColor: '#000000',
        plain: true,
        shape: 'round'
      },
        {
          text: '确定',
          backgroundColor: '#548ceb',
          fontColor: '#FFFFFF'
        }
      ],
      addClerkShow: false,
      formAdd: {
        phone: '',
        clerk_name: '',
      },
      id:'',
      select_id: 0,
      select_name: '',
      select_show: false,
      select_list: [],
      list: []
    }
  },
  onLoad() {
    this.getClark();
  },
  methods: {
    getClark() {
      var userInfo = uni.getStorageSync('userInfo');
      businessListByMember({
        member_id: userInfo.id,
      })
          .then(res => {
            console.log(res);
            if (res.code == 1) {
              var key = res.data;
              const transformedSelectList = key.map(item => ({
                value: item.id,
                label: item.business_name
              }));
              this.select_name = transformedSelectList[0].label;
              this.select_id = transformedSelectList[0].value;
              this.select_list = transformedSelectList;
              this.getList();
            }
          })
          .catch(error => {
            uni.showToast({
              title: error,
              icon: 'none',
              duration: 2000
            });
          })
    },
    add_confirm(d) {
      console.log(d);
      var info = d[0];
      this.select_name = info.label;
      this.select_id = info.value;
    },
    add() {
      if (this.formAdd.phone == '' || this.formAdd.clerk_name == '') {
        uni.showToast({
          title: '请填写店员信息',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      addClerk({
        business_id: this.select_id,
        ...this.formAdd
      })
          .then(res => {
            console.log(res);
            if (res.code == 1) {
              uni.showToast({
                title: '添加成功！',
                icon: 'none',
                duration: 2000
              });
              this.addClerkShow = false;
              this.getList();
            } else {
              uni.showToast({
                title: res.msg,
                icon: 'none',
                duration: 2000
              });
            }
          })
          .catch(error => {
            uni.showToast({
              title: error,
              icon: 'none',
              duration: 2000
            });
          })
    },
    del(id) {
      this.id=id;
      this.show = true;
    },
    dian(d) {
      console.log(d);
      this.show = false;
      if (d.index == 1) {
          this.delDo();
      }
    },
    delDo(){
      deleteClerk({
        id: this.id
      })
          .then(res => {
            console.log(res);
            if (res.code == 1) {
              uni.showToast({
                title: '删除成功！',
                icon: 'none',
                duration: 2000
              });
              this.getList();
            }else{
              uni.showToast({
                title: res.msg,
                icon: 'none',
                duration: 2000
              });
            }
          })
          .catch(error => {
            uni.showToast({
              title: error,
              icon: 'none',
              duration: 2000
            });
          })
    },
    getList() {
      clerkListByBusiness({
        business_id: this.select_id
      })
          .then(res => {
            console.log(res);
            if (res.code == 1) {
              this.list = res.data;
            }
          })
          .catch(error => {
            uni.showToast({
              title: error,
              icon: 'none',
              duration: 2000
            });
          })
    },
    open_phoen(telephone) {
      uni.makePhoneCall({
        phoneNumber: telephone, // 电话号码
        success: function () {
          console.log('拨打电话成功');
        },
        fail: function () {
          console.log('拨打电话失败');
        }
      });
    },
    goBack() {
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        uni.redirectTo({
          url: '/pages/index/index'
        })

      }
    },
  }
}
</script>

<style>

</style>
