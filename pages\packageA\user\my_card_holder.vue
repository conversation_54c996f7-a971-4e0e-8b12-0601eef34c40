<template>
	<view class="template-edit tn-safe-area-inset-bottom">
		<!-- 顶部自定义导航 -->
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'名片夹'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view v-for="(item,index) in list" :key="index" class="wallpaper-shadow tn-margin tn-padding">
				<view @click="tn('/pages/packageA/user/my_card?id='+item.id)"
					class="tn-classify__content__sub-classify__content__item tn-flex tn-flex-center  tn-flex-col-center">
					<!-- 标题，有需要可以显示出来 -->
					<view style="width: 100rpx;height: 100rpx">
						<image :src="apiImgUrl+item.photo_image"
							style="width: 100rpx;height: 100rpx;border-radius:50%;">
						</image>
					</view>
					<view style="margin-left: 20rpx;">
						<view style="font-size: 28rpx;">
							<text>{{item.nikename}}</text>
							<text style="margin-left: 50rpx;">{{item.phone}}</text>
						</view>
						<view class="tn-text-ellipsis-2" style="font-size: 24rpx;margin-top: 10rpx;">
							<text>{{item.enterprise_name}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		businessCard
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				uid: 0,
				apiImgUrl: this.$store.state.imgUrl,
				list: []
			}
		},
		onLoad() {
			var uid = uni.getStorageSync('uid');
			this.uid = uid;
			this.getBusinessCard();
		},
		methods: {
			tn(e) {
				uni.navigateTo({
					url: e
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
			getBusinessCard() {
				businessCard({
						member_id: this.uid
					})
					.then(res => {
						console.log(res);
						this.list = res.data;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
		}
	}
</script>

<style>

</style>
