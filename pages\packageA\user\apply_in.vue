<template>
	<view class="template-edit tn-safe-area-inset-bottom">
		<!-- 顶部自定义导航 -->
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:info.association_name+'入会申请'}]" :current="topCurrent" activeColor="#000"
						:bold="false" :fontSize="30"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>

		<view class="tn-safe-area-inset-bottom" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="margin-top: 40rpx">
				<tn-steps :list="tab_list" :current="current"></tn-steps>
			</view>
			<view v-if="current==1" style="padding: 30rpx">
				<view v-html="info.ruhuixizhu"></view>
				<view style="text-align: center;margin-top: 60rpx">
					<tn-checkbox :size="40" v-model="checked1">同意入会协议</tn-checkbox>
					<view style="margin-top: 30rpx">
						<tn-button backgroundColor="#01BEFF" fontColor="#ffffff" @click="one_next()">下一步</tn-button>
					</view>
				</view>
			</view>
			<view v-if="current==2">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">推荐人</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<input v-model="new_info.recommendation_name" placeholder="选填" :showRightIcon="false"
						:disabled="true" @click="tuijian_show = true" />
					<tn-select v-model="tuijian_show" mode="single" :list="member_list"
						@confirm="getTuiJianId"></tn-select>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">手机号</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<input placeholder="手机号" disabled v-model="userInfo.phone"
						placeholder-style="color:#AAAAAA"></input>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">真实姓名</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<input placeholder="请输入真实姓名" v-model="new_info.nikename" placeholder-style="color:#AAAAAA"></input>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">性别</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<input v-model="new_info.gender_name" :showRightIcon="false" :disabled="true"
						@click="gender_show = true" />
					<tn-select v-model="gender_show" mode="single" :list="genderList"
						@confirm="getGenderId"></tn-select>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">企业名称</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<input placeholder="请输入企业名称" v-model="new_info.enterprise_name"
						placeholder-style="color:#AAAAAA"></input>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">所属行业</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<view class="tn-flex  tn-flex-center tn-flex-col-center" @click="industry_id_show = true">
						<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">
							{{formData.industrynew_id_name}}
						</view>
					</view>
					<tn-select v-model="industry_id_show" mode="single" :list="industry_list"
						@confirm="getindustryId"></tn-select>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-margin">
					<view class="tn-flex justify-content-item">
						<view class="tn-padding-right-xs tn-text-bold">申请理由</view>
					</view>
				</view>
				<view class="tn-margin tn-bg-gray--light" style="border-radius: 10rpx;padding: 20rpx 30rpx;">
					<tn-input v-model="new_info.reason" :maxLength="1000" placeholder="申请入会理由" :clearable="false"
						:height="300" type="textarea" :customStyle="{lineHeight:'42rpx',color:'#777777'}" />
				</view>
				<div style="text-align: center;margin-top: 40px">
					<tn-button @click="current=1" backgroundColor="#E6E6E6" fontColor="#000000">上一步</tn-button>
					<tn-button backgroundColor="#01BEFF" fontColor="#ffffff" style="margin-left: 40rpx"
						@click="newSubmit">提交申请
					</tn-button>
				</div>
			</view>
		</view>
		<view class='tn-tabbar-height'></view>
	</view>
</template>

<script>
	import {
		associationInfo,
		IndustryNewList,
		loginAdd,
		positionList,
		MemberList
	} from '@/util/api.js';
	import store from "@/store";
	import string from "@/tuniao-ui/libs/function/string";

	export default {
		data() {
			return {
				positionList: [],
				industry_list: [],
				jiantuan_time_show: false,
				position_show: false,
				gender_show: false,
				industry_id_show: false,
				genderList: [{
					label: '男',
					value: 1
				}, {
					label: '女',
					value: 0
				}],
				birth_time_show: false,
				checked1: false,
				topCurrent: 0,
				current: 1,
				info: {},
				tab_list: [{
					name: '入会须知'
				}, {
					name: '人员信息'
				}],
				new_info: {
					recommendation_id: 0,
					recommendation_name: '',
					phone: '',
					nikename: '',
					enterprise_name: '',
					gender_name: '请选择',
					gender: 0,
					industry_id: 0,
					reason: '',
					industrynew_id: 0,
				},
				formData: {
					position_name: '请选择',
					position_id: 0,
					industry_id: '',
					industry_id_name: '请选择',
					industrynew_id_name: '请选择',
					phone: '',
					password: '',
					nikename: '',
					gender_name: '请选择',
					gender: 0,
					birth_time_name: '请选择',
					birth_time: '',
					nation: '',
					political: '',
					jiguan: '',
					institution: '',
					education: '',
					academic_degree: '',
					card_number: '',
					work_unit: '',
					unit_position: '',
					wx_number: '',
					mailbox: '',
					fixed_telephone: '',
					achievement_award: '',
					other_contacts: '',
					other_social_positions: '',
					position: '',
					cardf_image_show: [],
					cardz_image_show: [],
					photo_image_show: [],
					cardf_image: '',
					cardz_image: '',
					photo_image: '',
					business_license_image_show: [],
					business_license_image: '',
					unified_code: '',
					enterprise_nature: '',
					enterprise_website: '',
					if_list: '',
					employee: '',
					previous_revenue: '',
					previous_tax: '',
					previous_profit: '',
					previous_donation: '',
					qitaqiyerenzhiqingkuang: '',
					enterprise_Introduction: '',
					introdiction: '',
					enterprise_honor: '',
					if_organization: null,
					nature: '',
					jiantuan_time: '',
					jiantuan_time_name: '请选择',
					jiantuan_number: '',
					youth_number: '',
					superior_nature: '',
					tuanweifuzerenxinxi: '',
					documents_file: '',
					documents_file_show: [],
					enterprise_location: '',
					apiUrl: this.$store.state.apiUrl,

				},
				tuijian_show: false,
				member_list: [],
				Gid: 0,
				userInfo: {}
			}
		},
		onLoad(d) {
			console.log('123456', d);
			if (typeof(d.scene) != 'undefined') {
				let decodedParams = decodeURIComponent(d.scene);
				var searchParams = this.parseQuery(decodedParams);
				console.log('searchParams', searchParams);
				d = searchParams;
			}
			if (typeof(d.id) != 'undefined') {
				uni.setStorageSync('apply_id', d.id);
				this.new_info.recommendation_id = d.id;
			}

			this.userInfo = uni.getStorageSync('userInfo');
			if (typeof(d.association_id) != 'undefined') {
				store.commit('$tStore', {
					name: 'Gid',
					value: d.association_id
				})
				uni.setStorageSync('Gid', d.association_id);
				this.Gid = d.association_id;
			} else {
				var gid = uni.getStorageSync('Gid');
				this.Gid = gid;
			}
			getApp().getUserLogin((r) => {
				console.log('---Login---', r);
			})
			this.getAssociationInfo();
			this.getPlan();
			this.getIndustry();
			this.getMemberList();
		},
		methods: {
			parseQuery(queryStr) {
				let params = {};
				queryStr.split('&').forEach(param => {
					let [key, value] = param.split('=');
					params[key] = decodeURIComponent(value);
				});
				return params;
			},
			getMemberList() {
				MemberList({
						association_id: this.Gid,
					})
					.then(res => {
						if (res.code == 1) {
							var key = res.data;
							var transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.nikename
							}));
							if (this.new_info.recommendation_id != 0) {
								var index = transformedSelectList.findIndex(item => item.value == this.new_info
									.recommendation_id);
								this.new_info.recommendation_name = transformedSelectList[index].label;
							}
							this.member_list = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getIndustry() {
				IndustryNewList()
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.industry_name
							}));
							this.industry_list = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			formatResponse(res, type) {
				console.log(res);
				if (res.code == 1) {
					uni.showToast({
						title: '上传成功',
						icon: 'none',
						duration: 2000
					});
				}
				if (type == 0) {
					this.formData.photo_image = res.data.url;
				} else if (type == 1) {
					this.formData.cardz_image = res.data.url;
				} else if (type == 2) {
					this.formData.cardf_image = res.data.url;
				} else if (type == 3) {
					this.formData.business_license_image = res.data.url;
				} else {
					this.formData.documents_file = res.data.url;
				}
				//return {url: res.data.fullurl};
			},
			radioGroupChange(d) {
				console.log(d);
			},
			getTuiJianId(d) {
				var info = d[0];
				this.new_info.recommendation_id = info.value;
				this.new_info.recommendation_name = info.label;
			},
			getPositionId(d) {
				var info = d[0];
				this.formData.position_name = info.label;
				this.formData.position_id = info.value;
			},
			getGenderId(d) {
				var info = d[0];
				this.new_info.gender_name = info.label;
				this.new_info.gender = info.value;
			},
			getindustryId(d) {
				console.log(d);
				var info = d[0];
				this.formData.industrynew_id_name = info.label;
				this.new_info.industrynew_id = info.value;
			},
			birth_time_chick(d) {
				console.log(d);
				this.formData.birth_time = d.date;
				this.formData.birth_time_name = d.date;
			},
			jiantuan_time_chick(d) {
				this.formData.jiantuan_time_name = d.date;
				this.formData.jiantuan_time = d.date;
			},
			one_next() {
				if (!this.checked1) {
					uni.showToast({
						title: '请同意入会协议',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				this.current = 2;
			},
			getPlan() {
				positionList({
						association_id: this.Gid,
					})
					.then(res => {
						if (res.code == 1) {
							var key = res.data;
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.position_name
							}));
							this.positionList = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getAssociationInfo() {
				associationInfo({
						association_id: this.Gid,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							if (key.association_desc.indexOf("<img") != -1) {
								key.association_desc = getApp().addWidthToImages(key.association_desc);
							}
							this.info = key;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			newSubmit() {
				this.new_info.phone = this.userInfo.phone;
				if (this.new_info.phone == '') {
					uni.showToast({
						title: '请填写手机号码！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				const phoneNumberRegex = /^1[3-9]\d{9}$/;
				if (!phoneNumberRegex.test(this.new_info.phone)) {
					uni.showToast({
						title: '手机号码输入有误！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.new_info.nikename == '') {
					uni.showToast({
						title: '请填写真实姓名！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.new_info.gender == '' || this.new_info.gender == 0) {
					uni.showToast({
						title: '请选择性别！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.new_info.enterprise_name == '') {
					uni.showToast({
						title: '请填写企业名称！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.new_info.industrynew_id == '' || this.new_info.industrynew_id == 0) {
					uni.showToast({
						title: '请选择行业！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.new_info.reason == '' || this.new_info.reason == 0) {
					uni.showToast({
						title: '请填写申请理由！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				this.new_info.association_id = this.Gid;
				this.new_info.openid = uni.getStorageSync('openid');
				loginAdd(this.new_info)
					.then(res => {
						if (res.code == 1) {
							uni.showModal({
								title: '提示',
								content: '申请成功，请关注站内信通知！',
								success: function(res) {
									uni.navigateTo({
										url: '/pages/index/index'
									});
								}
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'error',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			submit() {
				if (this.formData.business_license_image == '') {
					uni.showToast({
						title: '请上传营业执照！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.unified_code == '') {
					uni.showToast({
						title: '请输入统一社会信用代码！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.enterprise_nature == '') {
					uni.showToast({
						title: '请输入企业性质！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.industry_id == null) {
					uni.showToast({
						title: '请选择行业！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.enterprise_website == '') {
					uni.showToast({
						title: '请输入企业网址！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.if_list == null) {
					uni.showToast({
						title: '请选择是否上市！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.employee == '') {
					uni.showToast({
						title: '请输入员工人数！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.enterprise_location == '') {
					uni.showToast({
						title: '请输入企业注册地！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.previous_revenue == '') {
					uni.showToast({
						title: '请输入上年度营业额！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.previous_tax == '') {
					uni.showToast({
						title: '请输入上年度纳税额！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.previous_profit == '') {
					uni.showToast({
						title: '请输入上年度净利润！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.previous_donation == '') {
					uni.showToast({
						title: '请输入上年度公益性捐赠支出！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.qitaqiyerenzhiqingkuang == '') {
					uni.showToast({
						title: '请输入其他企业任职情况！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.enterprise_Introduction == '') {
					uni.showToast({
						title: '请输入企业介绍！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.introdiction == '') {
					uni.showToast({
						title: '请输入业务介绍！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.enterprise_honor == '') {
					uni.showToast({
						title: '请输入企业所获荣誉以及专利！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.if_organization == null) {
					uni.showToast({
						title: '是否建立团组织！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.formData.if_organization == 0) {
					if (this.formData.nature == '') {
						uni.showToast({
							title: '请输主团组织性质！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.formData.jiantuan_time == '') {
						uni.showToast({
							title: '请输入建团时间！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.formData.jiantuan_number == '') {
						uni.showToast({
							title: '请输入建团人数！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.formData.youth_number == '') {
						uni.showToast({
							title: '请输入青年人数！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.formData.superior_nature == '') {
						uni.showToast({
							title: '请输入上级团组织！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.formData.tuanweifuzerenxinxi == '') {
						uni.showToast({
							title: '请输入团委负责人信息！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.formData.documents_file == '') {
						uni.showToast({
							title: '请上传批复文件！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}
				this.formData.association_id = this.id;
				loginAdd(this.formData)
					.then(res => {
						if (res.code == 1) {
							uni.showToast({
								title: '申请成功，请关注站内信通知！',
								icon: 'success',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'error',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					// uni.$emit('depId', {
					// 	index: 3
					// })
					uni.navigateBack();
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 底部悬浮按钮 start*/
	.tn-tabbar-height {
		min-height: 100rpx;
		height: calc(120rpx + env(safe-area-inset-bottom) / 2);
	}

	.tn-footerfixed {
		position: fixed;
		width: 100%;
		bottom: calc(30rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);

	}

	/* 底部悬浮按钮 end*/

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 20rpx 20rpx 5rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/
</style>