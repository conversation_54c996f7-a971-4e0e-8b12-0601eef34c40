<template>
	<view style="letter-spacing: 1rpx;background-color: #F2F2F2;min-height: 100vh;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'组织架构'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height+ 'px',}">
			<view style="position: fixed;width: 100%;background-color: #F2F2F2;z-index: 1;">
				<tn-tabs :list="list" :isScroll="false" :current="current" name="name" activeColor="#000000"
					:activeItemStyle="{backgroundColor:'#ffffff'}" :showBar='false' @change="change"></tn-tabs>
			</view>
			<view style="padding: 30rpx;text-align: center;padding-top: 94rpx;">
				<template v-for="(item, index) in architecture">
					<tn-button backgroundColor="#ffffff" height="50rpx"
						fontColor="#000000">{{item.position_name}}</tn-button>
					<tn-grid align="center" col="5" hoverClass="none">
						<tn-grid-item v-if="item.neirong" style="width:20%" v-for="(v,i) in item.neirong"
							@click="openInfo('/pages/packageA/user/my_card?id='+v.member_id)">
							<view style="padding: 30rpx;">
								<image :src="apiImgUrl+v.photo_image"
									style="width: 100rpx;height: 100rpx;border-radius: 50%;">
								</image>
								<view>{{v.nikename}}</view>
							</view>
						</tn-grid-item>
						<tn-grid-item v-if="!item.neirong" style="width:20%">
							<view style="padding: 30rpx;">
								<view>暂无</view>
							</view>
						</tn-grid-item>
					</tn-grid>
				</template>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		architectureList
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				topCurrent: 0,
				architecture: [],
				apiImgUrl: this.$store.state.imgUrl,
				current: 0,
				list: [{
					name: '理事会'
				}, {
					name: '监事会'
				}],
				type: 1
			}
		},
		onLoad(d) {
			this.getArchitectureList();
		},
		methods: {
			openInfo(u) {
				uni.navigateTo({
					url: u
				})
			},
			getArchitectureList() {
				architectureList({
						association_id: store.state.Gid,
						type: this.type,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.architecture = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
			change(index) {
				this.current = index;
				this.type = index + 1;
				this.architecture = [];
				this.getArchitectureList();
			}
		}
	}
</script>

<style>

</style>
