<template>
	<view style="letter-spacing: 1rpx;background: #ebf4f7;min-height: 100vh;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="false" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;width: 85%;">
					<view>审请详情</view>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding:20rpx 20rpx 300rpx 20rpx;">
				<view
					style="margin-bottom: 30rpx;box-shadow: 0rpx 10rpx 10rpx #d2e0f2;background: #ffffff;border-radius: 20rpx;padding: 30rpx 20rpx;position: relative;">
					<view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between">
						<view>
							<text
								style="font-size: 35rpx;font-weight: 600;">{{onloadData.type==1?'申请人信息':'推荐人信息'}}</text>
						</view>
					</view>
					<view style="width: 100%;margin: 20rpx auto;border: 1rpx solid #d9d9d961;"></view>
					<view v-if="info.find!=''" class="tn-flex tn-flex-center tn-flex-col-center"
						style="padding: 10rpx 0rpx;">
						<view>
							<image :src="apiImgUrl+info.find.photo_image"
								style="border-radius: 50%;width: 100rpx;height: 100rpx"></image>
						</view>
						<view style="margin-left: 20rpx;">
							<view>
								<text style="font-size: 32rpx;font-weight: 600;">{{info.find.nikename}}</text>
								<text
									style="font-size: 28rpx;color: #666666;margin-left: 20rpx;">{{info.find.position_name}}</text>

							</view>
							<view style="margin-top: 10rpx;">
								<text style="color: #4a5c72;">{{info.find.enterprise_name}}</text>
							</view>
						</view>
					</view>
					<view v-if="info.find==''">
						暂无推荐人
					</view>
				</view>
				<view
					style="margin-bottom: 30rpx;box-shadow: 0rpx 10rpx 10rpx #d2e0f2;background: #ffffff;border-radius: 20rpx;padding: 30rpx 20rpx;position: relative;">
					<view class="tn-flex tn-flex-center tn-flex-col-center">
						<view>
							<text style="font-size: 35rpx;font-weight: 600;">申请详情</text>
						</view>
						<view v-if="onloadData.type==2">
							<view v-if="info.member.if_xianshi==0"
								style="margin-left: 30rpx;text-align: center;line-height: 50rpx;width: 120rpx;height: 50rpx;color: #FFF;background-color: #FFBE77;border-radius: 10rpx;">
								审核中</view>
							<view v-if="info.member.if_xianshi==1"
								style="margin-left: 30rpx;text-align: center;line-height: 50rpx;width: 120rpx;height: 50rpx;color: #FFF;background-color: #3F8BF2;border-radius: 10rpx;">
								已通过</view>
							<view v-if="info.member.if_xianshi==2"
								style="margin-left: 30rpx;text-align: center;line-height: 50rpx;width: 120rpx;height: 50rpx;color: #FFF;background-color: #E60000;border-radius: 10rpx;">
								已拒绝</view>
						</view>
						<view v-if="onloadData.type==1">
							<view v-if="info.find.if_xianshi==0"
								style="margin-left: 30rpx;text-align: center;line-height: 50rpx;width: 120rpx;height: 50rpx;color: #FFF;background-color: #FFBE77;border-radius: 10rpx;">
								审核中</view>
							<view v-if="info.find.if_xianshi==1"
								style="margin-left: 30rpx;text-align: center;line-height: 50rpx;width: 120rpx;height: 50rpx;color: #FFF;background-color: #3F8BF2;border-radius: 10rpx;">
								已通过</view>
							<view v-if="info.find.if_xianshi==2"
								style="margin-left: 30rpx;text-align: center;line-height: 50rpx;width: 120rpx;height: 50rpx;color: #FFF;background-color: #E60000;border-radius: 10rpx;">
								已拒绝</view>
						</view>
					</view>
					<view style="width: 100%;margin: 20rpx auto;border: 1rpx solid #d9d9d961;"></view>
					<view style="padding: 10rpx 0rpx;">
						<view v-if="onloadData.type==2" style="margin-left: 20rpx;color: #5b5b5b;">
							<view style="margin-top: 15rpx;">
								<text style="">手机号：</text>
								<text>{{info.member.phone}}</text>
							</view>
							<view style="margin-top: 15rpx;">
								<text style="">真实姓名：</text>
								<text style="">{{info.member.nikename}}</text>
							</view>
							<view style="margin-top: 15rpx;">
								<text style="">性别：</text>
								<text style="">{{info.member.gender==0?'女':'男'}}</text>
							</view>
							<view style="margin-top: 15rpx;">
								<text style="">企业名称：</text>
								<text style="">{{info.member.enterprise_name}}</text>
							</view>
						</view>
						<view v-if="onloadData.type==1" style="margin-left: 20rpx;color: #5b5b5b;">
							<view v-if="info.member_log.background_image" class="tn-flex" style="margin-top: 10rpx;">
								<view style="width: 200rpx;">更改背景：</view>
								<image :src="apiImgUrl+info.member_log.background_image" style="height: 200rpx;"
									mode="heightFix"></image>
							</view>
							<view v-if="info.member_log.photo_image" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">形象照：</view>
								<image :src="apiImgUrl+info.member_log.photo_image"
									style="height: 100rpx;width: 100rpx;"></image>
							</view>
							<view v-if="info.member_log.nikename" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">姓名：</view>
								<view>{{info.member_log.nikename}}</view>
							</view>
							<view v-if="info.member_log.gender_name" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">性别：</view>
								<view>{{info.member_log.gender_name}}</view>
							</view>
							<view v-if="info.member_log.phone" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">联系方式：</view>
								<view>{{info.member_log.phone}}</view>
							</view>
							<view v-if="info.member_log.introduction" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">个人经历：</view>
								<view @click="infoShow = true;infoType=1;"
									style="padding: 10rpx;width: 500rpx;background-color: #F5F5F5;border-radius: 5rpx;">
									<view class="tn-text-ellipsis-3">{{info.member_log.introduction}}</view>
								</view>
							</view>
							<view v-if="info.member_log.enterprise_name" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">公司名称：</view>
								<view>{{info.member_log.enterprise_name}}</view>
							</view>
							<view v-if="info.member_log.industry_id" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">公司行业：</view>
								<view>{{info.member_log.industry_id==-1?'其他行业':info.member_log.industry_name}}</view>
							</view>
							<view v-if="info.member_log.company_image" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">公司Logo：</view>
								<image :src="apiImgUrl+info.member_log.company_image"
									style="height: 100rpx;width: 100rpx;"></image>
							</view>
							<view v-if="info.member_log.enterprise_location" class="tn-flex" style="margin-top: 30rpx;">
								<view style="width: 200rpx;">公司地址：</view>
								<view>{{info.member_log.enterprise_location}}</view>
							</view>
							<view v-if="info.member_log.enterprise_Introduction" class="tn-flex"
								style="margin-top: 30rpx;">
								<view style="width: 200rpx;">公司介绍：</view>
								<view @click="infoShow = true;infoType=2;"
									style="padding: 10rpx;width: 500rpx;background-color: #F5F5F5;border-radius: 5rpx;">
									<view class="tn-text-ellipsis-3">{{info.member_log.enterprise_Introduction}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="info.find.if_xianshi==0 || info.member.if_xianshi==0"
				style="position: fixed;background-color: #FFF;width: 100%;bottom: 0;padding-bottom: 100rpx;padding-top: 40rpx;">
				<view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-around"
					style="color: #FFF;text-align: center;">
					<view @click="openModal(2)"
						style="color: #ffffff;line-height: 88rpx;width: 138px;height: 88rpx;background: #d54941;border-radius: 8px;">
						拒绝申请</view>
					<view @click="openModal(1)"
						style="line-height: 88rpx;width: 138px;height: 88rpx;background: #3464ec;border-radius: 8px;">
						申请通过
					</view>
				</view>
			</view>
		</view>
		<tn-modal v-model="show" :title="title" :content="content" :button="button" @click="dian"></tn-modal>
		<tn-popup v-model="infoShow" mode="right" width="100%">
			<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="infoShow = false">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:infoType==1?'个人经历':'公司介绍'}]" :current="topCurrent" activeColor="#000"
							:bold="false" :fontSize="24"></tn-tabs>
					</view>
				</view>
			</tn-nav-bar>
			<view style="background: rgba(242, 241, 246, 1);height: 100%;padding-bottom: 100rpx;">
				<view :style="{paddingTop: vuex_custom_bar_height + 20+'px'}"></view>
				<view style="margin:30rpx;background-color: #ffffff;border-radius: 20rpx;">
					<view style="padding: 30rpx;">
						<view>
							{{infoType==1?info.member_log.introduction:info.member_log.enterprise_Introduction}}
						</view>
					</view>
				</view>
			</view>

		</tn-popup>
	</view>
</template>

<script>
	import {
		getAuditingFind,
		auditingUpadte
	} from '@/util/api.js';
	import store from "@/store";
	export default {
		data() {
			return {
				info: {
					find: []
				},
				apiImgUrl: this.$store.state.imgUrl,
				onloadData: {},
				show: false,
				infoShow: false,
				infoType: 1,
				title: '提示信息',
				content: '提示信息的内容',
				button: [{
						text: '取消',
						backgroundColor: '#F4F4F4',
						fontColor: '#000000',
						plain: true,
						shape: 'round'
					},
					{
						text: '确定',
						backgroundColor: '#548ceb',
						fontColor: '#FFFFFF'
					}
				],
				key: 0
			}
		},

		onLoad(d) {
			this.onloadData = d;
			this.getAuditingList();
		},
		methods: {
			dian(d) {
				if (d.index == 1) {
					this.setAuditing();
				} else {
					this.show = false;
				}
			},
			openModal(key) {
				this.key = key;
				this.show = true;
				this.title = key == 1 ? '通过申请' : '拒绝申请';
				this.content = key == 1 ? '确认要通过申请吗？' : '确认要拒绝申请吗？';
			},
			setAuditing() {
				var user_info = uni.getStorageSync('userInfo');
				var member_id = this.onloadData.type == 1 ? this.info.find.id : this.info.member.id;
				var content = '';
				if (this.key == 1) {
					content = this.onloadData.type == 1 ? '您提交的资料修改申请已通过审核！' : '您提交的入会申请已通过审核！';
				} else {
					content = this.onloadData.type == 2 ? '您提交的资料修改申请已被拒绝！' : '您提交的入会申请已被拒绝！';
				}
				auditingUpadte({
						...this.onloadData,
						association_id: user_info.association_id,
						member_id: member_id,
						if_xianshi: this.key,
						content: content
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
							uni.$emit('onload', 1)
							this.getAuditingList();
						}
						this.show = false;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getAuditingList() {
				var user_info = uni.getStorageSync('userInfo');
				getAuditingFind({
						...this.onloadData,
						association_id: user_info.association_id
					})
					.then(res => {
						this.info = res.data;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>
	/* 显示两行 */
	.tn-text-ellipsis-3 {
		display: -webkit-box;
		overflow: hidden;
		white-space: normal !important;
		text-overflow: ellipsis;
		word-wrap: break-word;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
	}
</style>