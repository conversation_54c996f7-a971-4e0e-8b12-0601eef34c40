<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'我参与的活动'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding:10px 30rpx;">
				<view v-for="(item,index) in list" class="tn-flex tn-flex-row-between"
					@click="openUrl('/pages/index/event_info?id='+item.activity_id)"
					style="background-color: #ffffff;padding: 20rpx;border-radius: 10rpx;margin-bottom: 30rpx;box-shadow: rgba(12, 0, 5, 0.1) 0px 0px 5px 0px;">
					<view style="position:relative;">
						<view class="tn-text-ellipsis-2" style="font-size: 28rpx;">{{item.activity_name}}</view>
						<view class="tn-flex tn-flex-row-between"
							style="position: absolute;bottom: 0rpx;width:420rpx;color: #808080;">
							<view style="font-size: 24rpx;padding-top: 8rpx;">{{item.application_time}}</view>
							<view style="margin-left: 10rpx">
								<tn-button v-if="item.type=='未开始'" padding="0px" width="100rpx" height="40rpx" size="sm"
									backgroundColor="#6BC7F0 " fontColor="tn-color-white">未开始</tn-button>
								<tn-button v-if="item.type=='进行中'" padding="0px" width="100rpx" height="40rpx" size="sm"
									backgroundColor="#EE9556 " fontColor="tn-color-white">进行中</tn-button>
								<tn-button v-if="item.type=='已结束'" padding="0px" width="100rpx" height="40rpx" size="sm"
									backgroundColor="#E12B33 " fontColor="tn-color-white">已结束</tn-button>
							</view>
						</view>
					</view>
					<view style="margin-left: 20rpx;">
						<image :src="apiImgUrl+item.activity_image"
							style="width: 200rpx;height: 135rpx;border-radius: 10rpx;">
						</image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		UserApplication
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				topCurrent: 0,
				list: [],
				apiImgUrl: this.$store.state.imgUrl,
			}
		},
		onLoad() {
			var uid = uni.getStorageSync('uid');
			this.uid = uid;
			this.getUserApplication();
		},
		methods: {
			getUserApplication() {
				UserApplication({
						member_id: this.uid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(e) {
				uni.navigateTo({
					url: e
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style>

</style>