<template>
	<view class="index">
		<view v-if="tabberPageLoadFlag[0]" :style="{display: currentIndex === 0 ? '' : 'none'}">
			<scroll-view class="custom-tabbar-page" scroll-y enable-back-to-top @scrolltolower="tabbarPageScrollLower">
				<Home ref="home"></Home>
			</scroll-view>
		</view>
		<!-- <view v-if="tabberPageLoadFlag[1]" :style="{display: currentIndex === 1 ? '' : 'none'}">
			<scroll-view class="custom-tabbar-page" scroll-y enable-back-to-top @scrolltolower="tabbarPageScrollLower">
				<Service ref="service"></Service>
			</scroll-view>
		</view> -->
		<view @touchmove="preventTouchMove" v-if=" tabberPageLoadFlag[1]"
			:style="{display: currentIndex === 1 ? '' : 'none'}">
			<view :class="tabShow?'custom-tabbar-page':''">
				<Directory ref="directory"></Directory>
			</view>
		</view>
		<!-- <view v-if="tabberPageLoadFlag[2]" :style="{display: currentIndex === 2 ? '' : 'none'}">
			<scroll-view class="custom-tabbar-page" scroll-y enable-back-to-top @scrolltolower="tabbarPageScrollLower">
				<Discovery ref="discovery"></Discovery>
			</scroll-view>
		</view> -->
		<view @touchmove="preventTouchMove" v-if="tabberPageLoadFlag[2]"
			:style="{display: currentIndex === 2 ? '' : 'none'}">
			<scroll-view scroll-y class="custom-tabbar-page" style="height: 100vh;" scroll-y enable-back-to-top
				@scrolltolower="tabbarPageScrollLower">
				<Pizz ref="pizz"></Pizz>
			</scroll-view>
		</view>
		<view v-if="tabberPageLoadFlag[3]" :style="{display: currentIndex === 3 ? '' : 'none'}">
			<scroll-view class="custom-tabbar-page" scroll-y enable-back-to-top @scrolltolower="tabbarPageScrollLower">
				<User ref="user"></User>
			</scroll-view>
		</view>

		<tn-tabbar :show="tabShow" :outHeight="140" :height="120" v-model="currentIndex" :list="tabbarList"
			activeColor="#000000" inactiveColor="#AAAAAA" activeIconColor="#3377FF" inactiveIconColor="#888888"
			:animation="true" :safeAreaInsetBottom="true" :thisIndex="thisIndex" @change="switchTabbar"></tn-tabbar>
	</view>
</template>

<script>
	import Home from './home.vue'
	import Service from './service.vue'
	import Pizz from './pizz.vue'
	import User from './user.vue'
	import Directory from './directory.vue'
	import {
		Mailcoent
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		components: {
			Home,
			Service,
			Pizz,
			User,
			Directory
		},
		data() {
			return {
				// 底部tabbar菜单数据
				tabbarList: [{
						title: '首页',
						activeIcon: '/static/01_1.png',
						inactiveIcon: '/static/01.png',
						id: 0,
					},
					{
						title: '通讯录',
						activeIcon: '/static/02_2.png',
						inactiveIcon: '/static/02.png',
						id: 1,
					},
					// {
					// 	// 服务、案例、品牌、合作、发现、探索
					// 	activeIcon: '',
					// 	inactiveIcon: '',
					// 	iconSize: 110,
					// 	out: true
					// },
					{
						title: '发现',
						activeIcon: '/static/03_3.png',
						inactiveIcon: '/static/03.png',
						id: 2,
					},
					{
						title: '个人中心',
						activeIcon: '/static/04_4.png',
						inactiveIcon: '/static/04.png',
						id: 3,
					}
				],
				thisIndex: 0,
				// tabbar当前被选中的序号
				currentIndex: 0,
				// 自定义底栏对应页面的加载情况
				tabberPageLoadFlag: [],
				apiImgUrl: this.$store.state.imgUrl,
				icon: '',
				name: '',
				tabShow: true,
			}
		},
		onResize(res) {
			console.log('onResizeonResizeonResize', res);
			let hdWidth = uni.getStorageSync('windowWidth')
			if (hdWidth != res.size.framesetWidth) {
				uni.setStorageSync('windowWidth', res.size.framesetWidth);
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		},
		onLoad(options) {
			console.log(options);
			const index = Number(options.index || 0)
			// 根据底部tabbar菜单列表设置对应页面的加载情况
			this.tabberPageLoadFlag = this.tabbarList.map((item, tabbar_index) => {
				return index === tabbar_index
			})
			this.switchTabbar(index)
			var that = this;
			uni.$on('depId', function(data) {
				that.switchTabbar(data.index);
			})
			uni.$on('showTab', function(data) {
				var index = data.index;
				that.tabShow = index;
			})
			if (typeof(options.scene) != 'undefined') {
				let decodedParams = decodeURIComponent(options.scene);
				var searchParams = this.parseQuery(decodedParams);
				store.commit('$tStore', {
					name: 'Gid',
					value: searchParams.gid
				})
				uni.setStorageSync('Gid', searchParams.gid);
			}
			if (typeof(options.gid) != 'undefined') {
				store.commit('$tStore', {
					name: 'Gid',
					value: options.gid
				})
				uni.setStorageSync('Gid', options.gid);
			}

		},
		mounted() {
			//this.wxshare();
		},
		onShareAppMessage() {
			console.log(store.state.Gid);
			return {
				title: this.name,
				path: '/pages/index/index?gid=' + store.state.Gid,
				imageUrl: this.icon,
			}
		},
		onShareTimeline() {
			console.log(store.state.Gid);
			return {
				title: this.name,
				path: '/pages/index/index?gid=' + store.state.Gid,
				imageUrl: this.icon,
			}
		},
		methods: {
			preventTouchMove() {},
			parseQuery(queryStr) {
				let params = {};
				queryStr.split('&').forEach(param => {
					let [key, value] = param.split('=');
					params[key] = decodeURIComponent(value);
				});
				return params;
			},
			childEvent(data) {
				console.log('childEvent');
				console.log(data);
				this.icon = this.apiImgUrl + data.icon;
				this.name = data.label;
			},
			// wxshare() {
			// 	this.$wxshare({
			// 		url: 'http://ysh.0rui.cn/#/pages/index/index',
			// 		data: {
			// 			url: window.location.href.split("#")[0],
			// 		},
			// 		share_data: {
			// 			title: '智慧云商协',
			// 			desc: '智慧云商协',
			// 			imgUrl: 'http://ysh.0rui.cn/static/ser.png',
			// 			link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
			// 		}
			// 	})
			// },
			// 切换导航
			switchTabbar(index) {
				this._switchTabbarPage(index)
			},


			// 瀑布流导航页面滚动到底部
			tabbarPageScrollLower(e) {
				console.log(123);
				if (this.currentIndex === 0) {
					this.$refs.home.ReachBottom();
				}
				if (this.currentIndex === 2) {
					this.$refs.pizz.ReachBottom();
				}
				// if (this.currentIndex === 1) {
				// 	this.$refs.service.ReachBottom();
				// }
			},

			// 切换导航页面
			_switchTabbarPage(index) {

				const selectPageFlag = this.tabberPageLoadFlag[index]
				this.thisIndex = this.tabbarList[index].id;
				if (selectPageFlag === undefined) {
					return
				}
				if (selectPageFlag === false) {
					this.tabberPageLoadFlag[index] = true
				}
				this.currentIndex = index
				if (index === 1) {
					setTimeout(() => {
						this.$refs.directory.onload();
					}, 100)
				}
				if (index === 3) {
					setTimeout(() => {
						this.$refs.user.onload();
					}, 100)
				}
				// if (index === 2) {
				// 	setTimeout(() => {
				// 		this.$refs.discovery.getNewInfo();
				// 	}, 100)
				// }

			}
		}
	}
</script>

<style lang="scss" scoped>
	.index {
		width: 100%;
		height: 100vh;
		position: relative;

		.custom-tabbar-page {
			width: 100%;
			// height: calc(100vh - 110rpx);
			height: 100%;
			box-sizing: border-box;
			padding-bottom: 0rpx;
			padding-bottom: calc(0rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(0rpx + env(safe-area-inset-bottom));
		}
	}
</style>