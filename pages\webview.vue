 <template>
	 <view class="contain">
	 	 <web-view src='https://wxaurl.cn/P5rAY9EBpPs'></web-view>
	 </view>
</template>

<script>
	export default {
		data() {
			return {
				data: '',
				miniProgramUrl: 'https://wxaurl.cn/P5rAY9EBpPs', // 替换为你的短链
				showWebView: false,
			}
		},
		mounted() {
			// openMiniProgramLink() 
		},
		methods: {
			openMiniProgramLink() {
				this.data = 'https://wxaurl.cn/P5rAY9EBpPs'
				// setTimeout(()=>{
				// 	uni.setNavigationBarTitle({
				// 		title:'协议'
				// 	})
				// },1000)
			},
		}
	}
</script>
// <script setup>
// 	import {
// 		ref
// 	} from 'vue';
// 	import { onLoad,onShow } from "@dcloudio/uni-app"
// 	const data = ref('')
// 	onLoad((option) => {
// 		data.value = 'https://wxaurl.cn/P5rAY9EBpPs '
// 		// setTimeout(()=>{
// 		// 	uni.setNavigationBarTitle({
// 		// 		title:'协议'
// 		// 	})
// 		// },1000)
// 	})
// </script>


<style>
</style>
<!-- <html>
<head>
  <meta charset="UTF-8">
  <title>跳转中...</title>
  <script>
    const targetUrl = new URLSearchParams(window.location.search).get('target');
    if (targetUrl) {
      window.location.href = 'https://wxaurl.cn/P5rAY9EBpPs ' ; // 自动跳转短链
    }
  </script>
</head>
<body>
  <p>正在跳转...</p>
</body>
</html> -->