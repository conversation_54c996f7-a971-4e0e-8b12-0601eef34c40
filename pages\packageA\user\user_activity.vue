<template>
	<view style="letter-spacing: 1rpx;background: #ebf4f7;min-height: 100vh;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="false" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;width: 85%;">
					<view>添加活动</view>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding: 30rpx;">
				<view style="background-color: #FFF;border-radius: 15rpx;padding: 20rpx;">
					<tn-form :labelWidth="140">
						<tn-form-item prop="activity_name">
							<tn-input v-model="formData.activity_name" :customStyle="{width: '600rpx'}"
								placeholder="请填写活动标题" />
						</tn-form-item>
						<tn-form-item label="活动主图">
							<tn-input :disabled="true" :clearable="false" @click="upload_img" />
							<view slot="right" @click="upload_img">
								<view>
									<text v-if="formData.activity_image==''">请上传活动主图</text>
									<image class="no-img" v-if="formData.activity_image!=''"
										:src="apiImgUrl+formData.activity_image"
										style="width: 100rpx;vertical-align: middle;" mode="widthFix"></image>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-form-item label="活动类型">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right">
								<view>
									<text>协会活动</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-form-item label="报名开始时间" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="start_sign_time_show = true">
								<view>
									<text>{{ formData.signup_start_time == '' ? '请选择报名开始时间' : formData.signup_start_time }}
									</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-picker mode="time" @confirm="start_sign_time_do" v-model="start_sign_time_show"
							:params="{year: true,month: true,day: true,hour: true,minute: true,second: false}"></tn-picker>
						<tn-form-item label="报名结束时间" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="end_sign_time_show = true">
								<view>
									<text>{{ formData.signup_end_time == '' ? '请选择报名结束时间' : formData.signup_end_time }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-picker mode="time" @confirm="end_sign_time_do" v-model="end_sign_time_show"
							:params="{year: true,month: true,day: true,hour: true,minute: true,second: false}"></tn-picker>

						<tn-form-item label="活动开始时间" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="start_time_show = true">
								<view>
									<text>{{ formData.activity_start_time == '' ? '请选择活动开始时间' : formData.activity_start_time }}
									</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-picker mode="time" @confirm="start_time_do" v-model="start_time_show"
							:params="{year: true,month: true,day: true,hour: true,minute: true,second: false}"></tn-picker>
						<tn-form-item label="活动结束时间" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="end_time_show = true">
								<view>
									<text>{{ formData.activity_end_time == '' ? '请选择活动结束时间' : formData.activity_end_time }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-picker mode="time" @confirm="end_time_do" v-model="end_time_show"
							:params="{year: true,month: true,day: true,hour: true,minute: true,second: false}"></tn-picker>
						<tn-form-item label="活动地址">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="open_mode(0)">
								<view>
									<text>{{ formData.activity_location == '' ? '请输入活动地址' : formData.activity_location }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-form-item label="显示报名人数" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="display_show = true">
								<view>
									<text>{{ check_display(formData.if_display_registrants,1) }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-select v-model="display_show" mode="single" :list="list" @confirm="display_do"
							:searchShow="true"></tn-select>

						<tn-form-item label="报名范围" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="display_range_show = true">
								<view>
									<text>{{ check_display(formData.range,2) }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-select v-model="display_range_show" mode="single" :list="range_list"
							@confirm="display_range_do" :searchShow="true"></tn-select>


						<tn-form-item label="报名审核" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="display_if_auditing_show = true">
								<view>
									<text>{{ check_display(formData.if_auditing,3) }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-select v-model="display_if_auditing_show" mode="single" :list="if_auditing_list"
							@confirm="display_if_auditing_do" :searchShow="true"></tn-select>
						<tn-form-item label="报名人数">
							<tn-input v-model="formData.number" :clearable="false" :customStyle="{width: '500rpx'}"
								placeholder="请填写报名人数(0为不限制)" />
						</tn-form-item>
						<tn-form-item label="签到" :labelWidth="200">
							<tn-input :disabled="true" :clearable="false" />
							<view slot="right" @click="display_if_sign_show = true">
								<view>
									<text>{{ check_display(formData.if_sign,4) }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</view>
						</tn-form-item>
						<tn-select v-model="display_if_sign_show" mode="single" :list="if_sign_list"
							@confirm="display_if_sign_do" :searchShow="true"></tn-select>
					</tn-form>
				</view>
				<view style="background-color: #FFF;border-radius: 15rpx;padding: 20rpx;margin-top: 25rpx;">
					<tn-input v-model="activity_content" :disabled="true" placeholder="请输入活动内容" type="textarea"
						:maxLength="-1" :height="300" @click="editText= true" :clearable="false" />
				</view>
				<view style="margin: 0 auto;margin-top: 40rpx;text-align: center;">
					<tn-button @click="submitDo" backgroundColor="#3F8BF2" height="90rpx" width="90%" shape="round"
						fontColor="#ffffff">发布
					</tn-button>
				</view>
			</view>
		</view>
		<tn-modal v-model="addMod" :custom="true">
			<view class="custom-modal-content">
				<view style="text-align: center;font-size: 34rpx;">活动地址</view>
				<view class="text">
					<tn-form ref="form" :labelWidth="180">
						<tn-form-item label="活动地址" prop="content">
							<tn-input type="text" placeholder="填写活动地址" v-model="formData.activity_location" />
						</tn-form-item>
					</tn-form>
					<view style="text-align: center;margin-top: 30rpx;">
						<tn-button backgroundColor="#E6E6E6" fontColor="#ffffff" @click="addMod = false">取消</tn-button>
						<tn-button backgroundColor="tn-bg-blue" fontColor="tn-color-white" style="margin-left: 30rpx"
							@click="addMod = false">确定
						</tn-button>
					</view>
				</view>
			</view>
		</tn-modal>
		<tn-popup v-model="editText" mode="right" width="100%">
			<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="editText = false">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:'活动内容'}]" :current="0" activeColor="#000" :bold="false"
							:fontSize="24"></tn-tabs>
					</view>
				</view>
			</tn-nav-bar>
			<view style="background-color: #FFFFFF;">
				<view :style="{paddingTop: vuex_custom_bar_height+'px'}"></view>
				<view style="margin:0rpx 30rpx;background-color: #ffffff;border-radius: 40rpx;">
					<scroll-view scroll-y class="home" style="height: 1200rpx;">
						<view class="editor-box">
							<sp-editor placeholder="活动内容" :toolbarConfig="{
          excludeKeys: ['direction', 'date', 'listCheck','link','export'],
          iconSize: '18px',
        }" @input="inputOver" @init="initEditor" @upinImage="upinImage"></sp-editor>
						</view>
					</scroll-view>
				</view>
			</view>
			<view style="margin: 0 auto;margin-top: 40rpx;text-align: center;">
				<tn-button @click="editTextRes" backgroundColor="#3F8BF2" height="90rpx" width="90%" shape="round"
					fontColor="#ffffff">确定
				</tn-button>
			</view>
		</tn-popup>
		<tn-modal v-if="qrcode_show" v-model="qrcode_show" :custom="true" :maskCloseable="false">
			<view style="text-align: center;margin: 30rpx 0rpx;font-size: 32rpx;font-weight: 600;">请保存签到二维码</view>
			<view style="text-align: center;width: 250rpx;margin:0rpx auto;">
				<l-painter ref="painter" css="background: #fff;width: 250rpx;text-align: center;">
					<l-painter-qrcode :text="qrcode_url" css="width: 250rpx; height: 250rpx" />
				</l-painter>
			</view>
			<view style="text-align: center;margin-top: 50rpx;">
				<tn-button @click="insQrcode" shape="round" width="80%" backgroundColor="#3F8BF2"
					fontColor="#ffffff">保存</tn-button>
			</view>
		</tn-modal>
	</view>
</template>

<script>
	import {
		activityAdd
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				qrcode_show: false,
				qrcode_url: '',
				editorIns: null,
				editText: false,
				addMod: false,
				end_time_show: false,
				start_time_show: false,
				start_sign_time_show: false,
				end_sign_time_show: false,
				display_if_auditing_show: false,
				display_range_show: false,
				display_show: false,
				display_if_sign_show: false,
				formData: {
					activity_name: '',
					activity_image: '',
					activity_content: '',
					activity_start_time: '',
					activity_end_time: '',
					signup_start_time: '',
					signup_end_time: '',
					activity_location: '',
					activity_type: 1,
					if_display_registrants: 1,
					range: 3,
					if_auditing: 1,
					number: '',
					if_sign: 1,
				},
				activity_content: '',
				apiImgUrl: this.$store.state.imgUrl,
				apiUpUrl: this.$store.state.apiUrl,
				list: [{
					value: 1,
					label: '显示'
				}, {
					value: 2,
					label: '不显示'
				}],
				range_list: [{
					value: 1,
					label: '会员'
				}, {
					value: 3,
					label: '不限制'
				}],
				if_auditing_list: [{
					value: 1,
					label: '否'
				}, {
					value: 2,
					label: '是'
				}],
				if_sign_list: [{
					value: 1,
					label: '否'
				}, {
					value: 2,
					label: '是'
				}],
				addID: 0,
			}
		},
		methods: {
			submitDo() {
				var user_info = uni.getStorageSync('userInfo');
				if (user_info.association_id == 0 || user_info.if_member == 2 || user_info.if_xianshi != 1) {
					uni.showToast({
						title: '暂无权限！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				activityAdd({
						association_id: user_info.association_id,
						...this.formData
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.addID = res.data;
							uni.showToast({
								title: '发布成功！',
								icon: 'none',
								duration: 2000
							});
							if (this.formData.if_sign == 2) {
								this.qrcode_url = store.state.apiUrl + '?id=' + res.data + '&association_id=' +
									user_info.association_id,
									setTimeout(() => {
										this.qrcode_show = true;
									}, 500)
							} else {
								setTimeout(() => {
									uni.redirectTo({
										url: '/pages/packageB/event/event_info?id=' + res.data
									})
								}, 1000)
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			inputOver(e) {
				// 可以在此处获取到编辑器已编辑的内容
				this.formData.activity_content = e.html;
			},
			editTextRes() {
				// 使用正则表达式找到所有的<img>标签，并替换为[图片]
				let replacedContent = this.formData.activity_content.replace(/<img[^>]*>/g, '[图片]');
				// 继续使用正则表达式移除所有剩余的HTML标签，但不包括刚刚替换的[图片]
				let textWithImagesOnly = replacedContent.replace(/<[^>]+>/g, '');
				this.activity_content = textWithImagesOnly;
				this.editText = false;
			},
			initEditor(editor) {
				this.editorIns = editor; // 保存编辑器实例
			},
			upinImage(tempFiles, editorCtx) {
				var that = this;
				uni.uploadFile({
					url: that.apiUpUrl + '/common/upload', //仅为示例，非真实的接口地址
					filePath: tempFiles[0].tempFilePath,
					name: 'file',
					success: (uploadFileRes) => {
						var data = JSON.parse(uploadFileRes.data);
						console.log(data);
						editorCtx.insertImage({
							src: data.data.fullurl,
							width: "80%", // 默认不建议铺满宽度100%，预留一点空隙以便用户编辑
							success: function() {},
						});
					}
				});
			},
			upload_img() {
				var user_info = uni.getStorageSync('userInfo');
				var that = this;
				uni.chooseMedia({
					count: 1, //默认9
					mediaType: ['image'],
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album'], //从相册选择
					success: function(res) {
						console.log(res);
						var url = res.tempFiles[0].tempFilePath;
						uni.uploadFile({
							url: that.apiUpUrl + '/common/upload', //仅为示例，非真实的接口地址
							filePath: url,
							name: 'file',
							formData: {
								association_id: user_info.association_id,
							},
							success: (uploadFileRes) => {
								var data = JSON.parse(uploadFileRes.data);
								console.log(data);
								that.formData.activity_image = data.data.url;
							}
						});
					}
				});
			},
			insQrcode() {
				var that = this;
				this.$refs.painter.canvasToTempFilePathSync({
					// 在nvue里是jpeg
					fileType: "png",
					quality: 1,
					pathType: 'url',
					success: (res) => {
						console.log(res.tempFilePath);
						// 非H5 保存到相册
						// H5 提示用户长按图另存
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: function() {
								uni.showToast({
									title: '保存成功！',
									icon: 'none',
									duration: 2000
								});
								setTimeout(() => {
									uni.redirectTo({
										url: '/pages/packageB/event/event_info?id=' + that
											.addID
									})
								}, 500)
							},
							fail: function(err) {
								console.log(err, '失败')
							}
						});
					},
				});
			},
			check_display(d, type) {
				if (type == 1) {
					const info = this.list.find(item => item.value === d);
					return info.label;
				}
				if (type == 2) {
					const info = this.range_list.find(item => item.value === d);
					return info.label;
				}
				if (type == 3) {
					const info = this.if_auditing_list.find(item => item.value === d);
					return info.label;
				}
				if (type == 4) {
					const info = this.if_sign_list.find(item => item.value === d);
					return info.label;
				}
			},
			display_do(d) {
				var info = d[0];
				this.formData.if_display_registrants = info.value;
			},
			display_range_do(d) {
				var info = d[0];
				this.formData.range = info.value;
			},
			display_if_auditing_do(d) {
				var info = d[0];
				this.formData.if_auditing = info.value;
			},
			display_if_sign_do(d) {
				var info = d[0];
				this.formData.if_sign = info.value;
			},
			open_mode(d) {
				this.addMod = true;
			},
			end_time_do(d) {
				this.formData.activity_end_time = d.year + '-' + d.month + '-' + d.day + ' ' + d.hour + ':' + d.minute;
			},
			start_time_do(d) {
				this.formData.activity_start_time = d.year + '-' + d.month + '-' + d.day + ' ' + d.hour + ':' + d.minute;
			},
			start_sign_time_do(d) {
				this.formData.signup_start_time = d.year + '-' + d.month + '-' + d.day + ' ' + d.hour + ':' + d.minute;
			},
			end_sign_time_do(d) {
				this.formData.signup_end_time = d.year + '-' + d.month + '-' + d.day + ' ' + d.hour + ':' + d.minute;
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>
	.no-img {
		width: 0;
		height: 0;
	}
</style>
