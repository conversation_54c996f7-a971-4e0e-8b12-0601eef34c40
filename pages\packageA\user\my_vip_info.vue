<template>
	<view class="message tn-safe-area-inset-bottom">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'商家详情'}]" activeColor="#000" :bold="false" :fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="padding: 30rpx;">
				<view class="tn-flex tn-flex-col-center tn-flex-row-between"
					style="background-color: #ffffff;padding:50rpx 30rpx;border-radius: 15rpx;margin-bottom: 20rpx;">
					<view class="tn-flex tn-flex-col-center">
						<view>
							<image :src="info.business_image==''?'/static/def.png':apiImgUrl+info.business_image"
								style="width: 100rpx;height: 100rpx;border-radius: 50%;">
							</image>
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 31rpx;font-weight: 600;letter-spacing: 5rpx;">{{info.business_name}}
							</view>
						</view>
					</view>
				</view>
				<view style="font-size: 24rpx;color: #808080;margin-top: 15rpx;" @click="openLoca()">
					<text>地址：{{info.address}}</text>
					<text class="tn-icon-location" style="margin-left: 10rpx;color:#3377FF"></text>
				</view>
				<view style="font-size: 24rpx;color: #808080;margin-top: 15rpx;" @click="openTel()">
					<text>电话：{{info.telephone}}</text>
					<text class="tn-icon-tel-circle" style="margin-left: 10rpx;color:#3377FF"></text>
				</view>
				<view style="margin-top: 30rpx;font-size: 30rpx;font-weight: 600;">商家介绍</view>
				<view style="margin-top: 20rpx;" v-html="info.introduction"></view>
				<view style="margin-top: 30rpx;font-size: 30rpx;font-weight: 600;">活动介绍</view>
				<view style="margin-top: 20rpx;" v-html="info.activity_content"></view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		businessFind
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				info: {},
				apiImgUrl: this.$store.state.imgUrl,
				id: 0,
			}
		},
		onLoad(data) {
			this.id = data.id;
			this.getbusinessFind();
		},
		methods: {
			openLoca() {
				uni.openLocation({
					latitude: parseFloat(this.info.address_latitude),
					longitude: parseFloat(this.info.address_longitude),
					name: this.info.business_name,
					success: function() {
						console.log('success');
					},
					fail: function(res) {
						console.log(res);
					}
				});
			},
			openTel() {
				uni.makePhoneCall({
					phoneNumber: this.info.telephone, // 电话号码
					success: function() {
						console.log('拨打电话成功');
					},
					fail: function() {
						console.log('拨打电话失败');
					}
				});
			},
			getbusinessFind() {
				businessFind({
						id: this.id,
					})
					.then(res => {
						console.log(res);
						var key = res.data;
						if (res.code == 1) {
							if (key.activity_content.indexOf("<img") != -1) {
								key.activity_content = getApp().addWidthToImages(key.activity_content);
							}
							this.info = key;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>

</style>