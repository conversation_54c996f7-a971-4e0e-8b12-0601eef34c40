<template>
	<view class="message tn-safe-area-inset-bottom">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'会员卡'}]" activeColor="#000" :bold="false" :fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view class="" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view style="text-align: center;padding: 30rpx;">
				<tn-button backgroundColor="#01BEFF" @click="openQrcode" fontColor="#ffffff">出示二维码</tn-button>
				<view style="margin-top: 20rpx;">会员卡号：{{ code }}</view>
			</view>
		</view>
		<tn-tabs :list="list" :isScroll="true" :activeItemStyle="{fontSize:'30rpx',fontWeight:'600'}"
			activeColor="#3377FF" :current="current" name="type_name" @change="change" :fontSize="28"></tn-tabs>
		<view style="padding: 30rpx;">
			<view v-for="(item,index) in sp_list" style="position: relative;"
				@click="tn('/pages/packageA/user/my_vip_info?id='+item.id)">
				<view class="tn-flex tn-flex-col-center tn-flex-row-between"
					style="background-color: #ffffff;padding:50rpx 30rpx;border-radius: 15rpx;margin-bottom: 20rpx;">
					<view class="tn-flex tn-flex-col-center">
						<view>
							<image :src="item.business_image==''?'/static/def.png':apiImgUrl+item.business_image"
								style="width: 100rpx;height: 100rpx;border-radius: 50%;">
							</image>
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 31rpx;font-weight: 600;letter-spacing: 5rpx;">
								{{ item.business_name }}
							</view>
							<view style="font-size: 24rpx;color: #808080;margin-top: 15rpx;">地址：{{ item.address }}
							</view>
							<view style="font-size: 24rpx;color: #808080;margin-top: 15rpx;">电话：{{ item.telephone }}
							</view>
						</view>
					</view>
					<view style="width: 200rpx;text-align: right;">
						<tn-button backgroundColor="#3377FF" size="sm" shape="round" fontColor="#ffffff">查看</tn-button>
					</view>
				</view>

				<!-- <view
          style="font-size: 20rpx;color: #43A9F8;background-color: #CAE5FF;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 10rpx;">
          <text class="tn-icon-success-circle-fill" style="vertical-align: middle;"></text>
          <text style="vertical-align: middle;margin-left: 5rpx;">已认证</text>
        </view> -->
			</view>
		</view>
		<tn-popup v-if="show" :closeBtn="true" v-model="show" mode="center" width="500rpx" height="600rpx">
			<view style="text-align: center;padding: 30rpx 0rpx;font-size: 32rpx;font-weight: 600;">核销二维码</view>
			<view style="text-align: center;width: 300rpx;margin:0rpx auto;">
				<l-painter ref="painter" css="background: #fff;width: 300rpx;text-align: center;">
					<l-painter-qrcode :text="qrcode_url" css="width: 300rpx; height: 300rpx" />
				</l-painter>
			</view>
			<view style="text-align: center;margin-top: 50rpx;">
				<tn-button @click="show= false" shape="round" width="80%" backgroundColor="#3F8BF2"
					fontColor="#ffffff">关闭
				</tn-button>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import {
		businesstypeList,
		businessList,
		handleMembercode
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				current: 0,
				show: false,
				qrcode_url: '',
				list: [],
				page: 1,
				type: 0,
				apiImgUrl: this.$store.state.imgUrl,
				sp_list: [],
				code: 0,
			}
		},
		onLoad() {
			this.getbusinesstypeList();
			this.getbusinessList();
			this.setHandleMembercode();
		},
		methods: {
			change(d) {
				this.current = d;
				var info = this.list[d];
				this.type = info.id;
				this.page = 1;
				this.sp_list = [];
				this.getbusinessList();
			},
			setHandleMembercode() {
				var u = uni.getStorageSync('userInfo');
				handleMembercode({
						member_id: u.id,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.code = res.data.member_code
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success: function(res) {
									that.goBack();
								}
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getbusinessList() {
				var u = uni.getStorageSync('userInfo');
				businessList({
						business_type: this.type,
						association_id: u.association_id,
						page: this.page,
						size: 10,
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.sp_list.push(...res.data.list);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getbusinesstypeList() {
				businesstypeList()
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							key.unshift({
								id: 0,
								type_name: '全部'
							});
							this.list = res.data;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			generateUUIDv4() {
				const charts = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
					'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
				];
				var res = '';
				for (var i = 0; i < 18; i++) {
					var id = Math.ceil(Math.random() * 35);
					res += charts[id];
				}
				return res;
			},
			openQrcode() {
				var u = uni.getStorageSync('userInfo');
				this.qrcode_url = store.state.imgUrl + "?type=2&key=" + this.code + "&association_id=" + u.association_id +
					"&random_number=" + this.generateUUIDv4();
				setTimeout(() => {
					this.show = true;
				}, 500)
			},
			tn(e) {
				uni.navigateTo({
					url: e
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>
	page {
		background-color: aliceblue;
	}
</style>