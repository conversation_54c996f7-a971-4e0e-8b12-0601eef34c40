<template>
	<view style="background: #F7F7F7;min-height: 100vh;">
		<tn-nav-bar customBack :bottomShadow="false" backgroundColor="#ffffff">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
				<view style="color: #000000;;text-align: left;font-size: 36rpx;font-weight: 600;">
					<text>惠企政策</text>
				</view>
			</view>
			<view slot="back" class='tn-custom-nav-bar__back' style="padding-left: 20rpx;" @click="goBack">
				<image src="/static/h_back.png" style="width: 60rpx;height: 60rpx;"></image>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 'px'}" style="padding-bottom: 60rpx;">
			<!-- <tn-list-view :card="true" unlined="all">
				<block v-for="(item, index) in list" :key="index">
					<tn-list-cell padding="36rpx" :arrow="true"
						@click="openUrl('/pages/packageA/info/policy_info?id='+item.id)">{{item.article_title}}</tn-list-cell>
				</block>
			</tn-list-view> -->
			<view style="padding: 30rpx;">
				<view v-for="(item, index) in list" :key="index" style="padding: 30rpx;background-color: #FFF;border-radius: 18rpx;margin-bottom: 30rpx;">
					<view class="tn-flex" @click="openUrl('/pages/packageA/info/policy_info?id='+item.id)">
						<view>
							<image src="/static/hqzc_f.png" style="width: 230rpx;height: 140rpx;border-radius: 10rpx;">
							</image>
						</view>
						<view style="padding-left: 20rpx;position: relative;">
							<view class="tn-text-ellipsis-2">{{item.article_title}}</view>
							<view style="position: absolute;bottom: 10rpx;width: 100%;">
								<view style="color: #9B9B9B;font-size: 24rpx;white-space: nowrap;">发布时间：{{formatTimestamp(item.create_time)}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getArticlePolicyList
	} from "@/util/api";
	import store from "@/store";
	export default {
		data() {
			return {
				list: []
			}
		},
		onLoad(d) {
			console.log(d);
			this.cid = d.id;
			this.getArticlePolicyListAll();
		},
		methods: {
			formatTimestamp(timestamp) {
			  const date = new Date(timestamp*1000);
			  const year = date.getFullYear(); 
			  const month = String(date.getMonth()  + 1).padStart(2, "0"); // 补零
			  const day = String(date.getDate()).padStart(2,  "0");
			  return `${year}.${month}.${day}`;
			},
			getArticlePolicyListAll() {
				getArticlePolicyList({
						cid: this.cid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list = res.data.ret;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},
		}
	}
</script>

<style>

</style>