<template>
	<view style="background-color: #EBF4F7;letter-spacing: 1rpx;">
		<view :style="{paddingTop: vuex_custom_bar_height +40+ 'rpx'}"
			style="background: linear-gradient(50deg, #034EF9 0%, #05ACFF 99%);height: 400rpx;">

			<view class="tn-flex tn-flex-center tn-flex-col-center" style="padding: 30rpx;width: 100%;">
				<view v-if="login && userInfo.photo_image">
					<image :src="apiImgUrl+userInfo.photo_image"
						@click="tn_ru('/pages/packageA/user/my_card?id='+userInfo.member_id)"
						style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
						<<!-- image :src="apiImgUrl+userInfo.photo_image"
							@click="handleRemind"
							style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image> -->
				</view>
				<view v-if="!login || !userInfo.photo_image">
					<image src="/static/def.png" style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
				</view>
				<view style="width: 100%;">
					<view v-if="login" class="tn-flex tn-flex-col-center tn-flex-row-between">
						<view style="margin-left: 20rpx;color: #fff"
							@click="tn_ru('/pages/packageA/user/my_card?id='+userInfo.member_id)">
							<!-- <view style="margin-left: 20rpx;color: #fff" @click="handleRemind"> -->
							<view style="font-size: 35rpx;">{{ userInfo.nikename }}</view>
							<view v-if="userInfo.position_name!=null&&userInfo.if_member!=2"
								style="font-size: 24rpx;margin-top: 10rpx;">
								{{userInfo.position_name}}
							</view>
							<view
								v-if="userInfo.if_member==2&&userInfo.association_id!=null&&userInfo.association_id!=0&&userInfo.if_xianshi==0"
								style="font-size: 24rpx;margin-top: 10rpx;">
								入会审核中
							</view>
							<view
								v-if="userInfo.association_id==null||userInfo.association_id==0&&userInfo.if_member==2"
								style="font-size: 24rpx;margin-top: 10rpx;">
								游客
							</view>
						</view>
						<view @click="tn('/pages/packageA/user/my_edit')">
						<!-- <view @click="handleRemind"> -->
							<image src="/static/ico13.png" style="width: 40rpx ;" mode="widthFix"></image>
						</view>
					</view>
					<view v-if="!login" class="tn-flex tn-flex-col-center tn-flex-row-between">
						<view style="margin-left: 20rpx;color: #fff">
							<!-- <tn-button @click="loginMod = true">点击登陆</tn-button> -->
							<!-- <tn-button open-type="getPhoneNumber" @getphonenumber="PhoneLogin">点击登陆</tn-button> -->
							<tn-button open-type="getPhoneNumber" @getphonenumber="PhoneLogin" @click="handleRemind()">点击登陆</tn-button>
						</view>
						<!--            <view @click="tn('/pages/index/my_edit')">-->
						<!--              <image src="/static/ico13.png" style="width: 40rpx ;" mode="widthFix"></image>-->
						<!--            </view>-->
					</view>
				</view>
			</view>
		</view>
		<view>
			<view style="padding: 30rpx;margin-top:-115rpx;background-color: transparent;">
				<view class="tn-flex tn-flex-center tn-flex-col-center tn-flex-row-between"
					style="text-align: center;padding: 30rpx;background-color: #FFF;border-radius: 20rpx;box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(12,0,5,0.1);">
					<!-- <view @click="tn('/pages/index/pizz_info?id='+userInfo.association_id)"> -->
					<view @click="handleRemind()">
						<view>
							<image src="/static/ico5.png" style="width: 50rpx;height: 50rpx;"></image>
						</view>
						<view style="margin-top: 10rpx;">
							<text>所在商/协会</text>
						</view>
					</view>
					<!-- <view @click="tn('/pages/packageA/user/events_my')"> -->
					<view @click="handleRemind()">
						<view>
							<image src="/static/ico1.png" style="width: 50rpx;height: 50rpx;"></image>
						</view>
						<view style="margin-top: 10rpx;">
							<text>参与活动</text>
						</view>
					</view>
					<!-- <view @click="tn('/pages/packageA/user/my_desc')"> -->
					<view @click="handleRemind()">
						<view>
							<image src="/static/02_2.png" style="width: 50rpx;height: 50rpx;"></image>
						</view>
						<view style="margin-top: 10rpx;">
							<text>协会服务</text>
						</view>
					</view>
					<!-- <view @click="tn('/pages/packageA/user/my_card?id='+userInfo.member_id)"> -->
					<view @click="handleRemind()">
						<view>
							<image src="/static/ico6.png" style="width: 50rpx;height: 50rpx;"></image>
						</view>
						<view style="margin-top: 10rpx;">
							<text>我的名片</text>
						</view>
					</view>
				</view>
			</view>
			<view style="background-color: #fff;padding-bottom: 0rpx;">
				<view style="padding:0px 20rpx;">
					<!-- <tn-list-cell v-if="login" :arrow="true" @click="tn('/pages/packageB/ask/user_list')"> -->
					<tn-list-cell v-if="login" :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/icon100.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">我的提问</view>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell v-if="login" :arrow="true" @click="tn('/pages/packageA/user/my_card_holder')"> -->
					<tn-list-cell v-if="login" :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/icon13.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">名片夹</view>
						</view>
					</tn-list-cell>
					<tn-list-cell v-if="login" :arrow="true" @click="tn('/pages/packageA/user/my_edit')">
					<!-- <tn-list-cell v-if="login" :arrow="true" @click="handleRemind()"> -->
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/icon14.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">信息修改</view>
						</view>
					</tn-list-cell>
					<!--					<tn-list-cell :arrow="true" @click="tn('/pages/index/my_pizz')">-->
					<!--						<view class="tn-flex tn-flex-center tn-flex-col-center">-->
					<!--							<image src="/static/ico7.png" style="width: 50rpx;height: 50rpx"></image>-->
					<!--							<view style="margin-left: 20rpx;">所在商/协会</view>-->
					<!--						</view>-->
					<!--					</tn-list-cell>-->
					<!-- <tn-list-cell :arrow="true" v-if="login && userInfo.if_xianshi!=1"
						@click="tn_ru('/pages/packageA/user/apply_in?association_id='+loginData.association_id)"> -->
						<tn-list-cell :arrow="true" v-if="login && userInfo.if_xianshi!=1" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/ico8.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">入会申请</view>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell :arrow="true" @click="tn('/pages/packageA/user/my_msg')"> -->
					<tn-list-cell :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center" style="position: relative;">
							<image src="/static/ico9.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">我的消息</view>
							<tn-badge v-if="msg()>0" style="position: absolute;right: 40rpx;width: 30rpx;height: 30rpx"
								backgroundColor="#E83A30" fontColor="#ffffff">{{msg()}}</tn-badge>
						</view>
					</tn-list-cell>
					<tn-list-cell :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center" style="position: relative;">
							<image src="/static/allianceMall.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">联盟商城</view>
							<tn-badge v-if="msg()>0" style="position: absolute;right: 40rpx;width: 30rpx;height: 30rpx"
								backgroundColor="#E83A30" fontColor="#ffffff">{{msg()}}</tn-badge>
						</view>
					</tn-list-cell>
					<tn-list-cell :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center" style="position: relative;">
							<image src="/static/allianceCoupon.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">联盟优惠券</view>
							<tn-badge v-if="msg()>0" style="position: absolute;right: 40rpx;width: 30rpx;height: 30rpx"
								backgroundColor="#E83A30" fontColor="#ffffff">{{msg()}}</tn-badge>
						</view>
					</tn-list-cell>
					<tn-list-cell :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center" style="position: relative;">
							<image src="/static/messageSupply.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">信息供应</view>
							<tn-badge v-if="msg()>0" style="position: absolute;right: 40rpx;width: 30rpx;height: 30rpx"
								backgroundColor="#E83A30" fontColor="#ffffff">{{msg()}}</tn-badge>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell v-if="login" :arrow="true" @click="tn('/pages/packageA/user/my_invite')"> -->
					<tn-list-cell v-if="login" :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/ico10.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">邀请入会</view>
						</view>
					</tn-list-cell>
					<!--					<tn-list-cell :arrow="true" @click="tn('/pages/index/my_assist')">-->
					<!--						<view class="tn-flex tn-flex-center tn-flex-col-center">-->
					<!--							<image src="/static/ico10.png" style="width: 50rpx;height: 50rpx"></image>-->
					<!--							<view style="margin-left: 20rpx;">常见问题</view>-->
					<!--						</view>-->
					<!--					</tn-list-cell>-->
					<!-- <tn-list-cell v-if="userInfo.shenhe==1&&login" :arrow="true"
						@click="tn('/pages/packageA/user/user_auditing')"> -->
						<tn-list-cell v-if="userInfo.shenhe==1&&login" :arrow="true"
							@click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/icon15.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">内容审核</view>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell v-if="userInfo.shenhe==1&&login" :arrow="true"
						@click="tn('/pages/packageA/user/user_activity')"> -->
						<tn-list-cell v-if="userInfo.shenhe==1&&login" :arrow="true"
							@click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/ico16.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">发布活动</view>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell :arrow="true" @click="tn('/pages/packageA/user/my_idea')"> -->
					<tn-list-cell :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/ico12.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">意见反馈</view>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell v-if="userInfo.if_business==1" :arrow="true"
						@click="tn('/pages/packageA/user/my_vip')">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/vip.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">会员卡</view>
						</view>
					</tn-list-cell> -->
					<!-- <tn-list-cell v-if="userInfo.business==1" :arrow="true"
						@click="tn('/pages/packageA/user/my_vip_clerk')"> -->
						<tn-list-cell v-if="userInfo.business==1" :arrow="true"
							@click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/ico12.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">店员管理</view>
						</view>
					</tn-list-cell>
					<!-- <tn-list-cell :arrow="true" @click="loginAut"> -->
					<tn-list-cell :arrow="true" @click="handleRemind()">
						<view class="tn-flex tn-flex-center tn-flex-col-center">
							<image src="/static/ico11.png" style="width: 50rpx;height: 50rpx"></image>
							<view style="margin-left: 20rpx;">退出帐号</view>
						</view>
					</tn-list-cell>
				</view>
			</view>
		</view>
		<tn-popup v-model="loginMod" mode="bottom" :safeAreaInsetBottom="true">
			<view class="custom-modal-content">
				<view style="font-size: 30rpx;padding: 30rpx 30rpx 0rpx 30rpx;font-weight: 600;letter-spacing: 2rpx">
					会员登陆</view>
				<view class="text" style="padding: 40rpx;">
					<tn-form ref="form" :labelWidth="120">
						<tn-form-item prop="phone">
							<tn-input placeholder="手机号" :customStyle="{width: '660rpx'}" v-model="loginData.phone" />
						</tn-form-item>
						<view style="height: 20rpx;"></view>
						<tn-form-item prop="password">
							<tn-input placeholder="密码" :customStyle="{width: '660rpx'}" :passwordIcon="false"
								v-model="loginData.password" type="password" />
						</tn-form-item>
						<tn-form-item prop="code">
							<tn-input v-model="code" :customStyle="{width: '400rpx'}" type="text"
								placeholder="请输入验证码"></tn-input>

							<view slot="right">
								<view @click="getcheckCode"
									style="letter-spacing: 2px; font-size: 42rpx; background-color: antiquewhite; padding: 5rpx 15rpx;">
									<text v-for="(item,index) in identifyCode" :style="{color:colors[index]}">
										{{ item }}
									</text>
								</view>
							</view>
						</tn-form-item>
					</tn-form>
					<view style="text-align: center;margin-top: 50rpx;">
						<!-- <tn-button backgroundColor="#E6E6E6" fontColor="#ffffff"
							@click="loginMod = false">取消</tn-button> -->
						<tn-button style="border-radius: 0px;" height="80rpx" backgroundColor="#EEF0F2" width="100%"
							fontColor="#000000" @click="submitLogin">确定</tn-button>
					</view>
				</view>
			</view>
			<view style="height: 120rpx;"></view>
		</tn-popup>
		<view @click="callPhone"
			style="text-align: center;padding-bottom: 100rpx;font-size: 24rpx;color: rgba(153,153,153,0.5);letter-spacing: 2rpx;padding-top: 60rpx;">
			<view>v{{vuex_version}}</view>
			<view>技术支持：洛商协工作小组</view>
			<view>电话：15503791530</view>
		</view>
	</view>
</template>

<script>
	import {
		getUserIndex,
		loginDo,
		wxphoneLogin
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				topCurrent: 0,
				login: false,
				loginMod: false,
				userInfo: {},
				apiImgUrl: this.$store.state.imgUrl,
				loginData: {
					openid: '',
					phone: '',
					password: '',
					association_id: store.state.Gid
				},
				code: '',
				identifyCode: '',
				identifyCodeName: '',
				colors: ['#00CCFF', '#FF0000', '#FF9933', '#33CC99'], // 可以根据需要增加颜色
			}
		},
		mounted() {

			//this.getcheckCode();
		},
		methods: {
			onload() {
				var uid = uni.getStorageSync('uid');
				this.uid = uid;
				if (uid) {
					this.getUserInfo();
				}
			},
			handleRemind() {
				uni.showToast({
					title: '请敬请期待',
					icon:'none',
					duration: 2000 // 设置显示时长为2000毫秒
				})
			},
			PhoneLogin(d) {
				console.log(d);
				var openid = uni.getStorageSync('openid');
				wxphoneLogin({
						code: d.code,
						openid: openid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '登陆成功！',
								icon: 'none',
								duration: 2000
							});
							uni.setStorageSync('uid', res.data.id);
							uni.setStorageSync('userInfo', res.data);
							this.userInfo = res.data;
							this.login = true;
							this.loginMod = false;
						} else {
							uni.showToast({
								title: '登陆失败！',
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getcheckCode() {
				let code = [];
				const codeLength = 4;
				var codeName = '';
				const random = [
					1, 2, 3, 4, 5, 6, 7, 8, 9, 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I',
					'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
					'X', 'Y', 'Z',
				];
				for (let i = 0; i < codeLength; i++) {
					let index = Math.floor(Math.random() * 34);
					code.push(random[index]);
					codeName += random[index];
				}
				this.identifyCodeName = codeName;
				this.identifyCode = code;
			},
			msg() {
				return this.$store.state.msgCount;
			},
			getUserInfo() {
				getUserIndex({
						member_b_id: this.uid,
						member_id: this.uid
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.userInfo = res.data;
							this.login = true;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			submitLogin() {
				console.log(this.code);
				console.log(this.identifyCodeName);
				if (this.code.toLowerCase() !== this.identifyCodeName.toLowerCase()) {
					uni.showToast({
						title: '验证码错误！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.loginData.phone == '') {
					uni.showToast({
						title: '请填写手机号！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.loginData.password == '') {
					uni.showToast({
						title: '请填写密码！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				this.loginData.openid = uni.getStorageSync('openid');
				loginDo(this.loginData)
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '登陆成功！',
								icon: 'none',
								duration: 2000
							});
							uni.setStorageSync('uid', res.data.id);
							uni.setStorageSync('userInfo', res.data);
							this.userInfo = res.data;
							this.login = true;
							this.loginMod = false;
						} else {
							uni.showToast({
								title: '帐号或密码错误！',
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			loginAut() {
				var that = this;
				uni.showModal({
					title: '提示',
					content: '确定要退出吗？',
					success: function(res) {
						if (res.confirm) {
							uni.removeStorageSync('uid');
							that.login = false;
							that.loginData.openid = '';
							that.loginData.phone = '';
							that.loginData.password = '';
							that.code = '';
							that.getcheckCode();
						}
					}
				});
			},
			tn_ru(e) {
				var url = "/pages/packageA/user/apply_in?association_id=" + store.state.Gid;
				uni.navigateTo({
					url: e
				})
			},
			tn(e) {
				var uid = uni.getStorageSync('uid');
				console.log(this.userInfo);
				console.log(this.login);
				if (!this.login) {
					uni.showToast({
						title: '请登陆后查看',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.userInfo.if_member == 2) {
					if (e == '/pages/packageA/user/my_edit') {
						uni.showToast({
							title: '请入会后查看',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (e.includes("/pages/packageA/user/my_invite")) {
						uni.showToast({
							title: '请入会后查看',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (e.includes("/pages/index/pizz_info")) {
						uni.showToast({
							title: '请入会后查看',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (e.includes("/pages/packageA/user/my_card")) {
						uni.showToast({
							title: '请入会后查看',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (e.includes("/pages/packageA/user/my_desc")) {
						uni.showToast({
							title: '请入会后查看',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}
				uni.navigateTo({
					url: e
				})
			}
		}
	}
</script>

<style>
	.my_input view {
		width: 100% !important;
	}

	.my_input input {
		width: 100% !important;
	}

	page {
		background-color: rgb(235, 244, 247);
	}
</style>