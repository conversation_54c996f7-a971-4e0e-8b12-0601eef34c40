import request from '@/util/request';
export const newsGoryList = data => request.post('/move/news/gory', data, false);
export const newsList = data => request.post('/move/news/index', data, false);
export const activityList = data => request.post('/move/activity/list', data, false);
export const activityIndex = data => request.post('/move/activity/index', data, false);
export const associationIndex = data => request.post('/move/association/index', data, false);
export const newsInfo = data => request.post('/move/news/find', data, false);
export const eventInfo = data => request.post('/move/activity/find', data, false);
export const associationInfo = data => request.post('/move/association/find', data, false);
export const architectureList = data => request.post('/move/address/architecture', data, false);
export const positionList = data => request.post('/position', data, false);
export const IndustryList = data => request.post('/industry', data, false);
export const IndustryNewList = data => request.post('/move/Industrynew', data, false);

export const loginAdd = data => request.post('/move/login/add', data, false);

export const loginDo = data => request.post('/move/login/login', data, false);
export const addressList = data => request.post('/move/address', data, false);
export const newIndustryIndex = data => request.post('/move/industry/index', data, false);
export const getUserIndex = data => request.post('/move/member/find', data, false);
export const UserApplication = data => request.post('/move/application', data, false);
export const applicationAdd = data => request.post('/move/application/add', data, false);
export const messageAdd = data => request.post('/move/message/add', data, false);

export const UserLogin = data => request.post('/move/login/wxlogin', data, false);

export const carouselIndex = data => request.post('/move/carousel', data, false);

export const setMaillogAdd = data => request.post('/move/maillog/add', data, false);
export const MaillogIndex = data => request.post('/move/maillog/index', data, false);
export const MaillogUpdate = data => request.post('/move/maillog/update', data, false);

export const MaillogUpdateState = data => request.post('/move/maillog/updatestate', data, false);

export const questionnaireFind = data => request.post('/move/questionnaire/questionnairefind', data, false);
export const questionnaireAdd = data => request.post('/move/questionnaire/appAdd', data, false);
export const moneyLogAdd = data => request.post('/move/money_log/add', data, false);
export const Mailcoent = data => request.post('/move/Maillog/mailcoent', data, false);
export const MemberList = data => request.post('/move/association/memberList', data, false);
export const getQrcode = data => request.post('/move/login/getUnlimitedQRCode', data, false);
//getSerachIndex
export const getSerachIndex = data => request.post('/move/index/index', data, false);

export const setUserEdit = data => request.post('/move/member_log/add', data, false);
export const businessCard = data => request.post('/move/member/businessCard', data, false);

export const getPosition = data => request.post('/move/position', data, false);

export const getArticlePolicy = data => request.post('/move/Article/gory', data, false);
export const getArticlePolicyList = data => request.post('/move/Article/index', data, false);
export const getArticlePolicyInfo = data => request.post('/move/Article/find', data, false);
export const getRegion = data => request.post('/move/region', data, false);

export const getAuditing = data => request.post('/move/auditing', data, false);

export const getAuditingFind = data => request.post('/move/auditing/find', data, false);

export const auditingUpadte = data => request.post('/move/auditing/auditing', data, false);

export const activityAdd = data => request.post('/move/activity/activityAdd', data, false);

export const wxphoneLogin = data => request.post('/move/login/wxphone', data, false);

export const activitySign = data => request.post('/move/activity_sign/index', data, false);

export const businesstypeList = data => request.post('/move/business/businesstypeList', data, false);

export const businessList = data => request.post('/move/business/businessList', data, false);

export const businessFind = data => request.post('/move/business/businessFind', data, false);

export const clerkListByBusiness = data => request.post('/move/business/getClerkListByBusinessId', data, false);

export const addClerk = data => request.post('/move/business/addClerk', data, false);

export const businessListByMember = data => request.post('/move/business/getBusinessListByMemberId', data, false);

export const deleteClerk = data => request.post('/move/business/deleteClerk', data, false);

export const handleMembercode = data => request.post('/move/Membercode/handleMembercode', data, false);

export const ifRightToExamine = data => request.post('/move/Writeoff/ifRightToExamine', data, false);

export const addWriteoff = data => request.post('/move/Writeoff/addWriteoff', data, false);

export const getNoticeList = data => request.post('/move/notice', data, false);

export const getNoticeInfo = data => request.post('/move/notice/find', data, false);

export const getNwwbList = data => request.post('/move/nwwb', data, false);

export const getNwwbAdd = data => request.post('/move/nwwb/add', data, false);

export const getNwwbInfo = data => request.post('/move/nwwb/find', data, false);

export const getNwwbUser = data => request.post('/move/nwwb/memberfind', data, false);