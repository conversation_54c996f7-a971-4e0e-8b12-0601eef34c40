<template>
	<!-- <view>212213</view> -->
	<view class="page-body">
		<!-- <tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;color: #000000"></text>
				</view>
			</view>
		</tn-nav-bar> -->
		<!-- <view style="width: 100%;height: 450rpx;margin-top: 30rpx;"> -->
		<view style="width: 100%;height: 450rpx;">
			<image src="https://ysx.0rui.cn/h5/static/personLogoBg.png" style="width: 100%;height: 450rpx;" mode="">
			</image>
		</view>
		<view class="joinUs-text">
			<view class="class-bg"></view>
			<text>入会申请</text>
		</view>

		<!-- <form bind:submitToGroup="onSubmitToGroup">
	    <view class="weui-cells__title">姓名</view>
		  <textarea value="{{userInfo.realname}}" />
	  </form> -->

		<view class="tn-form-class">
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">姓名</view>
				<view class="weui-cells weui-cells_after-title nameBox">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.name"
							style="padding: 15rpx;height: 90rpx;width: 100%;background-color: #fff;"
							bindinput="onBioInput" placeholder="请输入姓名(必填项)"></textarea>
						<!-- <view class="weui-cell weui-cell_input">
		    {{userInfo.realname}}
		    </view> -->
					</view>
				</view>
			</view>
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">手机号</view>
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.phone"
							style="padding: 15rpx;height: 90rpx;width: 100%;background-color: #fff;"
							bindinput="onBioInput" placeholder="请输入手机号(必填项)"></textarea>
						<!-- {{userInfo.phone}} -->
					</view>
				</view>
			</view>
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">性别</view>
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.gender"
							style="padding: 15rpx;height: 90rpx;width: 100%;background-color: #fff;"
							bindinput="onBioInput" placeholder="请输入性别"></textarea>
						<!-- {{userInfo.phone}} -->
					</view>
				</view>
			</view>
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">年龄</view>
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.age"
							style="padding: 15rpx;height: 90rpx;width: 100%;background-color: #fff;"
							bindinput="onBioInput" placeholder="请输入年龄(必填项)"></textarea>
						<!-- {{userInfo.idcard}} -->
					</view>
				</view>
			</view>
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">邮箱</view>
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.email"
							style="padding: 15rpx;height: 90rpx;width: 100%;background-color: #fff;"
							bindinput="onBioInput" placeholder="请输入手机号"></textarea>
						<!-- {{userInfo.phone}} -->
					</view>
				</view>
			</view>
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">工作</view>
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.work"
							style="padding: 15rpx;height: 90rpx;width: 100%;background-color: #fff;"
							bindinput="onBioInput" placeholder="请输入你的工作"></textarea>
						<!-- {{userInfo.idcard}} -->
					</view>
				</view>
			</view>
			<view class="page-section">
				<view class="weui-cells__title" style="margin-bottom: 16rpx;margin-left: 4rpx;">工作地点</view>
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell weui-cell_input">
						<textarea class="textarea" v-model="info.workLocation"
							style="padding: 10rpx;height: 90rpx;background-color: #fff;width: 100%;"
							bindinput="onBioInput" placeholder="请输入你的工作地点"></textarea>
						<!-- {{userInfo.idcard}} -->
					</view>
				</view>
			</view>
			<view style="height: 80rpx;width: 100%;background-color: #fff;"></view>

			<view style="width: 100%;padding: 20rpx 40rpx;position: fixed;bottom: 0;left: 0;z-index: 99;
				border-bottom: 0rpx;height: 120rpx;background-color: #fff;align-items: center;" @click="handleRemind()">
				<tn-button backgroundColor="#2368f2" fontColor="#fff" shape="round" width="100%" height="80rpx"
					class="saveEdit">提交信息</tn-button>
			</view>
		</view>






	</view>

</template>

<script>
	import {

		getNoticeList
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		data() {
			return {
				info: {
					name: null,
					phone: null,
					idcard: null,
					workLocation: null,
					work: null,
					age: null,
					gender: null,
					email: null,
				}
			}
		},
		mounted() {
			getApp().getUserLogin((r) => {
				console.log('---Login---', r);
			})
			this.getNotice();
			//this.getArticlePolicyListAll();
		},
		beforeDestroy() {
			// 移除事件监听器
			this.unregisterGetGidListener();
		},
		methods: {
			handleRemind() {
				uni.showToast({
					title: '请敬请期待',
					icon:'none',
					duration: 2000 // 设置显示时长为2000毫秒
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})
			
				}
			},
			getNotice() {
				getNoticeList({
						page: 1,
						size: 10
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.noticeList = res.data.ret;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
		}
	}
</script>

<style lang="scss" scoped>
	
	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 120rpx;
		height: calc(140rpx + env(safe-area-inset-bottom) / 2);
		height: calc(140rpx + constant(safe-area-inset-bottom));
	}
	
	.tn-footerfixed {
		position: fixed;
		width: 100%;
		bottom: calc(180rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);
	
	}
	
	.page-body {
		padding: 40rpx 20rpx;
		background-color: #ffffff1;
		width: 100%;
		height: 100%;

		.joinUs-text {
			display: flex;
			align-items: center;
			// padding: 10rpx;
			font-size: 36rpx;
			font-weight: 400;
			color: #323232;

			.class-bg {
				width: 12rpx;
				height: 50rpx;
				background-color: #0063BC;
				border-radius: 26rpx;
				margin-bottom: 20rpx;
				margin-top: 26rpx;
				margin-right: 20rpx;
			}
		}

		.tn-form-class {
			margin-bottom: 20rpx;
			background-color: #ffffff;

			.page-section {
				width: 100%;
				display: flex;
				margin-top: 40rpx;
				align-items: center;
				padding-left: 20rpx;
				align-items: center;
				justify-content: space-between;
				border-bottom: 2rpx solid #eeeeee;

				.weui-cells__title {
					font-size: 28rpx;
					font-weight: 400;
					color: #000;
					margin-bottom: 10rpx;
				}

				.weui-cells_after-title {
					width: 50%;
				}

				//   .page-section .nameBox{
				//     display: block;
				// 	font-size: 28rpx;
				// 	font-weight: 400;
				// 	color: #000;
				// 	margin-bottom: 10rpx;
				// 	background-color: #ffffff;
				//     /* justify-content: center; 水平居中 */
				//   }

				//   .page-section .nameBox .textarea {
				//     width: 50%; /* 设置宽度，避免占满整行 */
				//     height: 60rpx;
				//     display: flex;
				//   }
			}

			.saveEdit {
				width: 90%;
				height: 100rpx;
			}
		}


	}
</style>