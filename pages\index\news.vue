<template>
	<view style="background-color: rgb(235, 244, 247);">
		<view style="padding:0rpx 28rpx;margin-top: 20rpx;">
			<view style="background-color: #ffffff;border-radius: 15rpx 15rpx 0px 0px;padding:20rpx;">
				<tn-tabs :list="goryList" :isScroll="true" :activeItemStyle="{fontSize:'30rpx',fontWeight:'600'}"
					activeColor="#3377FF" :current="current" name="name" @change="change" :fontSize="28"></tn-tabs>
			</view>
		</view>
		<view style="padding:0px 28rpx;padding-bottom: 20rpx;min-height:1000rpx">
			<view v-for="(item,index) in news_list" :class="{ 'rounded_corner': index === 0 }"
				style="background-color: #ffffff;">
				<view class="tn-flex" @click="openNewUrl(item)" style="padding: 20rpx;min-height: 200rpx;">
					<view v-if="item.news_image">
						<image :src="apiImgUrl+item.news_image" mode="aspectFill"
							style="width: 230rpx;height: 180rpx;border-radius: 8rpx;"></image>
					</view>
					<view style="position:relative;padding:0rpx 10rpx 10rpx 15rpx;margin-left: 10rpx;">
						<view class="tn-text-ellipsis-3" style="font-size: 28rpx;">{{ item.news_title }}</view>
						<view class="tn-flex tn-flex-row-between"
							style="font-size:24rpx;width: 95%;color: rgb(171, 171, 171);position: absolute; bottom: 0rpx; min-width: 380rpx;overflow: hidden">
							<view>{{ item.name }}</view>
							<view>{{ formatTime(item.showtime) }}</view>
							<view>
								<text class="tn-icon-eye" style="vertical-align: middle;"></text>
								<text style="vertical-align: middle;">{{ item.news_hits }}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-if="news_list.length-1>index"
					style="background: rgba(217, 217, 217, 0.5);width: 90%;margin: 0 auto;height: 2rpx;"></view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		newsGoryList,
		newsList,
		associationIndex,
		Mailcoent,
		getArticlePolicyList,
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		data() {
			return {
				list: [],
				selectShow: false,
				selectList: [],
				topCurrent: 0,
				searlist: [{
					article_title: '会员名称/协会名称/活动/新闻'
				}],
				cardCur: 0,
				isAndroid: true,
				goryList: [],
				news_list: [],
				current: 0,
				page: 1,
				size: 10,
				gory_id: 0,
				type: 1,
				HomeTitle: '',
				apiImgUrl: this.$store.state.imgUrl,
				association_id: this.$store.state.Gid,
			}
		},
		mounted() {
			getApp().getUserLogin((r) => {
				console.log('---Login---', r);
			})
			this.registerGetGidListener();
			this.getNewsGoryList();
			this.getAssociationIndex();
			this.getMsg();
			//this.getArticlePolicyListAll();
		},
		beforeDestroy() {
			// 移除事件监听器
			this.unregisterGetGidListener();
		},
		methods: {
			itemClick(item) {
				console.log(item);
				uni.navigateTo({
					url: "/pages/packageB/news/notice_info?id=" + item.id
				})
			},
			registerGetGidListener() {
				const that = this;
				uni.$on('getGid', function(data) {
					console.log('-----', data);
					that.page = 1;
					that.goryList = [];
					that.news_list = [];
					that.association_id = data.gid;
					that.getNewsGoryList();
					that.getAssociationIndex();
				});
			},
			unregisterGetGidListener() {
				uni.$off('getGid');
			},
			openNewUrl(item) {
				//'/pages/packageB/news/new_info?id='+item.news_id
				console.log(item);
				if (item.wx_url == '') {
					uni.navigateTo({
						url: '/pages/packageB/news/new_info?id=' + item.news_id
					})
				} else {
					uni.navigateTo({
						url: '/pages/index/web?url=' + encodeURIComponent(item.wx_url),
					})
				}
			},
			callPhone() {
				uni.makePhoneCall({
					phoneNumber: '15503791530', // 电话号码
					success: function() {
						console.log('拨打电话成功');
					},
					fail: function() {
						console.log('拨打电话失败');
					}
				});
			},
			msg() {
				return this.$store.state.msgCount;
			},
			getArticlePolicyListAll() {
				getArticlePolicyList({
						cid: 22
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.list = res.data.ret;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openInfo(d) {
				uni.navigateTo({
					url: '/pages/packageA/info/policy_info?id=' + d.id,
				})
			},
			openRight() {
				uni.navigateTo({
					url: '/pages/packageA/info/policy',
				})
			},
			async getMsg() {
				var uid = uni.getStorageSync('uid');

				const res = await Mailcoent({
					member_id: uid
				});
				console.log(res);
				if (res.code == 1) {
					this.$store.commit('$tStore', {
						name: 'msgCount',
						value: res.data
					})
				} else {
					this.$store.commit('$tStore', {
						name: 'msgCount',
						value: 0
					})
				}
				console.log(store.state.msgCount);
			},
			confirm(d) {
				var info = d[0];
				console.log(info);
				store.commit('$tStore', {
					name: 'Gid',
					value: info.value
				})
				uni.setStorageSync('Gid', info.value);
				this.association_id = info.value;
				this.page = 1;
				this.goryList = [];
				this.news_list = [];
				this.getNewsGoryList();
				this.getAssociationIndex();
				//this.$emit('childEvent', '修改后的数据');
			},
			getAssociationIndex() {
				associationIndex({
						page: 1,
						size: 200
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							key.push({
								id: 61,
								association_name: '洛阳市总商会'
							});
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.association_name,
								icon: item.association_image
							}));
							const foundNumber = transformedSelectList.find((element) => element.value == store.state
								.Gid);
							this.HomeTitle = foundNumber.label;
							this.selectList = transformedSelectList;
							//#ifdef MP-WEIXIN
							this.$parent.childEvent(foundNumber);
							//#endif
							// #ifdef  H5
							console.log('H5');
							this.$parent.$parent.$parent.$parent.childEvent(foundNumber);
							// #endif
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getNewsGoryList() {
				newsGoryList({
						association_id: this.association_id
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							var key = res.data;
							key.unshift({
								id: 0,
								name: '最新'
							});
							this.goryList = key;
							this.type = 1;
							this.gory_id = 0;
							this.getNewsList();
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getNewsList() {
				newsList({
						association_id: store.state.Gid,
						type: this.type,
						gory_id: this.gory_id,
						page: this.page,
						size: this.size
					})
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							this.news_list.push(...res.data.ret);
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			cardSwiper(d) {
				this.cardCur = d.detail.current;
			},
			ReachBottom() {
				console.log('home');
				//this.page = this.page + 1;
				//this.getNewsList();
				//this.getIndex();
			},
			change(e) {
				this.current = e;
				var info = this.goryList[e];
				this.gory_id = info.id;
				this.type = e === 0 ? 1 : 0;
				this.page = 1;
				this.news_list = [];
				this.getNewsList();
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			openApply() {
				var uid = uni.getStorageSync('uid');
				var userInfo = uni.getStorageSync('userInfo');
				if (!uid || !userInfo) {
					uni.showToast({
						title: '请登陆后查看',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				uni.navigateTo({
					url: '/pages/packageA/user/apply_in?association_id=' + this.association_id
				})

			},
			opentab() {
				uni.$emit('depId', {
					index: 1
				})
			},
			videoErrorCallback(e) {
				uni.showModal({
					content: e.target.errMsg,
					showCancel: false
				})
			},
			formatTime(time) {
				const [year, month, day] = time.split(' ')[0].split('-');
				return `${year}-${month}-${day}`;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.my_tag {
		padding: 8rpx 15rpx;
		font-size: 20rpx;
		color: rgba(255, 255, 255, 1);
		border-radius: 0rpx 0rpx 22rpx 0rpx;
	}
</style>