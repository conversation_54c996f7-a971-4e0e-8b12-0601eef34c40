<template>
	<view class="page-c">
		<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;color: #ffffff"></text>
				</view>
				<view class="tn-margin-top" @click="goBack()">
					<tn-tabs :list="[{name:'会员名片'}]" :current="topCurrent" activeColor="#ffffff" :bold="false"
						:fontSize="24"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view style="height: 550rpx;">
			<view style="width: 100%;height: 450rpx;position: absolute;top: 0;">
				<image
						src="https://ysx.0rui.cn/h5/static/personLogoBg.png" style="width: 100%;height: 450rpx;opacity: 0.8;" mode="">
				</image>
				<!-- <image v-if="userThisInfo.background_image==''||userThisInfo.background_image==null"
					src="https://ysx.hschool.com.cn/uploads/bj.png" style="width: 100%;height: 450rpx;opacity: 0.8;"> -->
				<!-- <image v-if="userThisInfo.background_image==''||userThisInfo.background_image==null"
						src="https://ysx.0rui.cn/uploads/bj.png" style="width: 100%;height: 450rpx;opacity: 0.8;">
				</image>
				<image v-if="userThisInfo.background_image!=''&&userThisInfo.background_image!=null"
					:src="apiImgUrl+userThisInfo.background_image" style="width: 100%;height: 450rpx;opacity: 0.8;">
				</image> -->
				<view style="bottom:-105rpx;position: absolute;left: 28rpx;">
					<view class="tn-flex tn-flex-center tn-flex-col-center">
						<view style="width: 170rpx;height: 170rpx;position: relative;">
							<image v-if="userThisInfo.gender === 1" src="https://ysx.0rui.cn/h5/static/man.png"
								style="width: 100rpx;height: 100rpx;border-radius:50%;">
							</image>
							<image v-else src="https://ysx.0rui.cn/h5/static/weman.png"
								style="width: 100rpx;height: 100rpx;border-radius:50%;">
							</image>
							<!-- <image v-if="userThisInfo.photo_image!=null" :src="apiImgUrl+userThisInfo.photo_image"
								mode="aspectFill" style="width: 170rpx;height: 170rpx;border-radius: 50%;"></image>
							<image v-if="userThisInfo.photo_image==null" src="/static/def.png" mode="aspectFill"
								style="width: 170rpx;height: 170rpx;border-radius: 50%;"></image> -->
						</view>
						<view style="margin-left: 10rpx;margin-top: 30rpx;">
							<view style="font-size: 35rpx;color: #000000;font-weight: 600">{{userThisInfo.nikename}}
							</view>
							<view style="font-size: 28rpx;margin-top: 10rpx;">
								<text>协会职务：{{userThisInfo.position_name==null||userThisInfo.position_name==''?'无':userThisInfo.position_name}}</text>
								<text style="margin-left: 40rpx">性别：{{userThisInfo.gender==0?'女':'男'}}</text>
							</view>
							<!-- <view style="margin-top: 10rpx" v-if="userThisInfo.if_tongyi==0">
								联系方式：{{userThisInfo.phone}}</view>
							<view style="margin-top: 10rpx" v-if="userThisInfo.if_tongyi!=0">联系方式：***********</view> -->
							<view style="margin-top: 10rpx">联系方式：***********</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view style="padding: 30rpx 30rpx 240rpx 30rpx;">
			<view style="font-size: 30rpx;font-weight: 600;margin-bottom: 20rpx">个人经历</view>
			<view style="background-color: #ffffff;border-radius: 15rpx;width: 100%;margin: 0 auto;">
				<view style="padding: 20rpx;line-height: 50rpx;">
					<view class="tn-text-ellipsis-5" @click="editInfo=true;editType=1">
						<!-- {{userThisInfo.introduction}} -->
						{{userThisInfo.introduction==''||userThisInfo.introduction==null?'无':userThisInfo.introduction}}
					</view>
				</view>
			</view>
			<view style="font-size: 30rpx;font-weight: 600;margin-bottom: 20rpx;margin-top: 30rpx">企业介绍</view>
			<view style="background-color: #ffffff;border-radius: 15rpx;width: 100%;margin: 0 auto;">
				<view class="tn-flex tn-flex-center tn-flex-col-center" style="padding: 20rpx;">
					<view style="padding-left: 20rpx">
						<image v-if="userThisInfo.company_image!=''" :src="apiImgUrl+userThisInfo.company_image"
							mode="aspectFill" style="width: 160rpx;height: 160rpx;"></image>
						<view v-if="userThisInfo.company_image==''"
							style="width: 160rpx;height: 160rpx;background: #d9d9d9;"></view>
					</view>
					<view style="font-size: 24rpx;color: #666666;margin-left: 30rpx;">
						<view style="font-size: 36rpx;font-weight: 600;color: #000000">
							{{userThisInfo.enterprise_name==null||userThisInfo.enterprise_name==''?'未填写':userThisInfo.enterprise_name}}
						</view>
						<view style="margin: 15rpx 0rpx">
							所属行业：{{userThisInfo.industry_id==-1?'其他行业':userThisInfo.industry_name}}</view>
						<view>
							企业地址：{{userThisInfo.enterprise_location==null||userThisInfo.enterprise_location==''?'无':userThisInfo.enterprise_location}}
						</view>
					</view>
				</view>
			</view>
			<view style="background-color: #ffffff;border-radius: 15rpx;width: 100%;margin: 0 auto;margin-top: 30rpx;">
				<view style="padding: 20rpx;line-height: 50rpx;" @click="editInfo=true;editType=2">
					<view class="tn-text-ellipsis-10">
						{{userThisInfo.enterprise_Introduction==null||userThisInfo.enterprise_Introduction==''?'无':userThisInfo.enterprise_Introduction}}
					</view>
				</view>
			</view>
		</view>
		<view class="tn-flex" style="width: 100%;position: fixed;bottom: 0;">
			<view style="background: #418ef2;height: 100rpx;text-align: center;line-height: 100rpx;color: #ffffff;"
				class="tn-flex-8" @click="addModeShow">
				{{userThisInfo.if_tongyi!=0?'申请查看联系方式':'立刻联系'}}
			</view>
			<view style="background: #ffffff;height: 100rpx;text-align: center;line-height: 100rpx;" class="tn-flex-4">
				<tn-button openType="share" backgroundColor="#ffffff">
					<text class="tn-icon-send-fill" style="color: #418ef2;font-size: 40rpx;"></text>
				</tn-button>
			</view>
		</view>
		<view class="bg-tabbar-shadow"></view>
		<tn-modal v-model="addMod" :custom="true">
			<view class="custom-modal-content">
				<view style="text-align: center;font-size: 34rpx;">申请查看</view>
				<view class="text">
					<tn-form ref="form" :labelWidth="180">
						<tn-form-item label="自我介绍" prop="content">
							<tn-input type="text" placeholder="填写自我介绍" v-model="addData.content" />
						</tn-form-item>
					</tn-form>
					<view style="text-align: center;margin-top: 30rpx;">
						<tn-button backgroundColor="#E6E6E6" fontColor="#ffffff" @click="addMod = false">取消</tn-button>
						<tn-button backgroundColor="tn-bg-blue" fontColor="tn-color-white" style="margin-left: 30rpx"
							@click="add_log">确定
						</tn-button>
					</view>
				</view>
			</view>
		</tn-modal>
		<tn-popup v-model="editInfo" mode="right" width="100%">
			<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="editInfo = false">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:editType==1?'个人经历':'公司介绍'}]" :current="topCurrent" activeColor="#000"
							:bold="false" :fontSize="24"></tn-tabs>
					</view>
				</view>
			</tn-nav-bar>
			<view style="background: rgba(242, 241, 246, 1);height: 100%;">
				<view :style="{paddingTop: vuex_custom_bar_height +'px'}"></view>
				<view style="margin:30rpx;background-color: #ffffff;border-radius: 40rpx;">
					<scroll-view scroll-y="true" style="height: 600rpx;padding: 30rpx;">
						<view style="line-height: 45rpx;">
							<view v-if="editType==1">
								{{userThisInfo.introduction==''||userThisInfo.introduction==null?'无':userThisInfo.introduction}}
							</view>
							<view v-if="editType==2">
								{{userThisInfo.enterprise_Introduction==''||userThisInfo.enterprise_Introduction==null?'无':userThisInfo.enterprise_Introduction}}
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="tn-flex tn-flex-center tn-flex-row-center" style="color: #ffffff;">
					<view @click="editInfo = false"
						style="width: 300rpx;height: 80rpx;background: #d9d9d9;text-align: center;line-height: 80rpx;">
						关闭</view>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import {
		getUserIndex,
		setMaillogAdd
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				addMod: false,
				topCurrent: 0,
				uid: 0,
				this_uid: 0,
				userInfo: {},
				userThisInfo: {},
				apiImgUrl: this.$store.state.imgUrl,
				is: false,
				editInfo: false,
				editType: 1,
				addData: {
					content: '',
					association_id: store.state.Gid,
					member_q_id: 0,
					member_b_id: 0,
					type: 1
				}
			}
		},
		onLoad(d) {
			this.this_uid = d.id;
			var uid = uni.getStorageSync('uid');
			console.log(uid);
			if (!uid) {
				getApp().getUserLogin((r) => {
					console.log('---Login---', r);
					this.uid = r.id;
				})
			} else {
				this.uid = uid;
			}
			if (typeof(d.gid) != 'undefined') {
				store.commit('$tStore', {
					name: 'Gid',
					value: d.gid
				})
				uni.setStorageSync('Gid', d.gid);
			}
			this.getUserInfo();
		},
		onShareAppMessage(res) {
			var info = this.info;
			console.log(info);
			return {
				title: this.userThisInfo.nikename,
				path: '/pages/packageA/user/my_card?id=' + this.userThisInfo.member_id + '&gid=' + this.userThisInfo
					.association_id,
				imageUrl: this.apiImgUrl + this.userThisInfo.photo_image
			}
		},
		onShareTimeline() { // 分享到朋友圈
			return {
				title: this.userThisInfo.nikename,
				path: '/pages/packageA/user/my_card?id=' + this.userThisInfo.member_id + '&gid=' + this.userThisInfo
					.association_id,
				imageUrl: this.apiImgUrl + this.userThisInfo.photo_image
			}
		},
		methods: {
			addModeShow() {
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					uni.showToast({
						title: '请登录！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.userThisInfo.if_tongyi == 0) {
					this.callPhone();
					return;
				}
				this.addMod = true;
			},
			add_log() {
				var uid = uni.getStorageSync('uid');
				if (!uid) {
					uni.showToast({
						title: '请登录！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				this.addData.member_q_id = this.uid;
				this.addData.member_b_id = this.this_uid;
				setMaillogAdd(this.addData)
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '申请成功！',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}
						this.addMod = false;
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			callPhone() {
				// 电话号码为示例，请替换为实际需要拨打的号码
				const phoneNumber = this.userThisInfo.phone;
				uni.makePhoneCall({
					phoneNumber: phoneNumber, // 电话号码
					success: function() {
						console.log('拨打电话成功');
					},
					fail: function() {
						console.log('拨打电话失败');
					}
				});
			},
			getUserInfo() {
				getUserIndex({
						member_b_id: this.this_uid,
						member_id: this.uid ? this.uid : 0,
					})
					.then(res => {
						console.log(res)
						if (res.code == 1) {
							this.userThisInfo = res.data;
							// if (this.this_uid != this.uid) {
							// 	this.getThisUserInfo();
							// } else {
							// 	this.is = true;
							// 	this.userThisInfo = res.data;
							// }

						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}

		}
	}
</script>

<style lang="scss" scoped>
	.page-c {
		background-color: #EBF4F7;
		min-height: 100vh;
		// background-image: url('../../../static/characterIntroductionGg.png');
		// background-size: 100% 100%;
	}

	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 120rpx;
		height: calc(140rpx + env(safe-area-inset-bottom) / 2);
		height: calc(140rpx + constant(safe-area-inset-bottom));
	}

	.tn-footerfixed {
		position: fixed;
		width: 100%;
		bottom: calc(180rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);

	}

	/* 按钮 */
	.button-1 {
		background-color: rgba(0, 0, 0, 0.15);
		position: fixed;
		/* bottom:200rpx;
    right: 20rpx; */
		bottom: 27%;
		right: 30rpx;
		z-index: 1001;
		border-radius: 100px;
	}

	.button-2 {
		background-color: rgba(0, 0, 0, 0.15);
		position: fixed;
		/* bottom:200rpx;
    right: 20rpx; */
		bottom: 18%;
		right: 30rpx;
		z-index: 1001;
		border-radius: 100px;
	}

	/* 图标容器15 start */
	.icon15 {
		&__item {
			width: 30%;

			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 100rpx;
				height: 100rpx;
				font-size: 50rpx;
				border-radius: 50%;
				margin-bottom: 18rpx;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;


				}
			}
		}
	}


	/* 相册 */
	.slideshow {
		overflow: hidden;
		text-align: center;
	}


	/* 简历内容 */
	.king-list {
		display: block;
		// background-color: #ffffff;
	}

	.king-list .king-icon {
		width: 100%;
		text-align: left;
		padding: 20rpx 0 20rpx 37rpx;
		font-size: 26rpx;
		color: #888;
		display: block;
	}

	.king-list>.king-item {
		padding: 30rpx 30rpx 30rpx 120rpx;
		position: relative;
		display: block;
		z-index: 0;
	}

	.king-list>.king-item::after {
		content: "";
		display: block;
		position: absolute;
		width: 1rpx;
		background-color: #E6E6E6;
		left: 60rpx;
		height: 100%;
		top: 0;
		z-index: 8;
	}

	.king-list>.king-item::before {
		display: block;
		position: absolute;
		top: 36rpx;
		z-index: 9;
		background-color: #ffffff;
		width: 50rpx;
		height: 50rpx;
		text-align: center;
		border: none;
		line-height: 50rpx;
		left: 36rpx;
	}


	/* 名片微调 */
	.img-solid {
		border: 1rpx solid #eee;
	}

	.share-img {
		position: fixed;
		/* padding: 10rpx; */
		width: 100rpx;
		height: 100rpx;
		/* top: 680rpx; */
		bottom: 200rpx;
		right: 20rpx;
		z-index: 1024;
		opacity: 0.8;
		box-shadow: 0rpx 8rpx 30rpx 0rpx rgba(0, 0, 0, 0.3);
		border: none;
		border: 6rpx solid rgba(255, 255, 255, 0);
	}

	.resume {
		display: flex;
		justify-content: space-between;
		padding-top: 10rpx;
		border-radius: 6rpx;
		color: #666;
		line-height: 1.6;
	}

	.resume+.resume {
		margin-top: 20rpx;
	}

	.resume2 {
		padding-top: 10rpx;
		border-radius: 6rpx;
		display: block;
		color: #666;
		line-height: 1.6;
		text-align: justify;
	}

	/* 间隔线 start*/
	.tn-strip-bottom {
		width: 100%;
		border-bottom: 5rpx solid #ffffff;
	}

	/* 间隔线 end*/


	.bg-img-cont {
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		height: 350rpx;
		margin: 20rpx 0;
		border-radius: 8rpx;
	}


	// .button-no {
	//   border: none;
	//   width: 100%;
	//   height: 100%;
	//   background-color: rgba(0, 0, 0, 0);
	// }


	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 0rpx 20rpx 25rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/

	.see {
		display: flex;
		justify-content: space-between;
		padding-top: 10rpx;
		border-radius: 6rpx;
		color: #666;
		line-height: 1.6;
	}

	/* 显示5行 */
	.tn-text-ellipsis-5 {
		display: -webkit-box;
		overflow: hidden;
		white-space: normal !important;
		text-overflow: ellipsis;
		word-wrap: break-word;
		-webkit-line-clamp: 5;
		-webkit-box-orient: vertical;
	}

	/* 显示5行 */
	.tn-text-ellipsis-10 {
		display: -webkit-box;
		overflow: hidden;
		white-space: normal !important;
		text-overflow: ellipsis;
		word-wrap: break-word;
		-webkit-line-clamp: 10;
		-webkit-box-orient: vertical;
	}
</style>