<template>
	<view>
		<view class="ask_index_bg">
			<!-- <tn-nav-bar customBack :bottomShadow="false" :backgroundColor="top">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-center">
					<view style="color: #000000;;text-align: left;font-size: 36rpx;font-weight: 600;">
						<text>你问我帮</text>
					</view>
				</view>
				<view slot="back" class='tn-custom-nav-bar__back' style="padding-left: 20rpx;" @click="goBack">
					<image v-if="top == 'none'" src="/static/w_back.png" style="width: 60rpx;height: 60rpx;"></image>
					<image v-if="top != 'none'" src="/static/h_back.png" style="width: 60rpx;height: 60rpx;"></image>
				</view>
			</tn-nav-bar> -->
			<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="false" :backgroundColor="top">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="goBack()">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top">
					<tn-tabs :list="[{name:'你问我帮'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
					</view>
				</view> 
			</tn-nav-bar>
			<view :style="{paddingTop: vuex_custom_bar_height + 'px'}">
				<view style="padding: 30rpx;">
					<view v-for="item in list" @click="openUrl('/pages/packageB/ask/info?id='+item.id)"
						style="background-color: #fff;border-radius: 18rpx;padding: 30rpx;margin-bottom: 30rpx;">
						<view>
							<image v-if="item.photo_image !='' && item.photo_image!=null"
								:src="apiImgUrl+item.photo_image"
								style="border-radius: 50%;width: 50rpx;height: 50rpx;vertical-align: middle;"
								mode="aspectFill"></image>
							<image v-if="item.photo_image =='' || item.photo_image==null" src="/static/def.png"
								style="border-radius: 50%;width: 50rpx;height: 50rpx;vertical-align: middle;"
								mode="aspectFill"></image>
							<text
								style="color: #9B9B9B;vertical-align: middle;margin-left: 10rpx;font-size: 28rpx;font-weight: 400;">{{item.member_name}}</text>
						</view>
						<view class="tn-flex" style="margin-top: 30rpx;">
							<view>
								<image src="/static/wen.png" style="width: 38rpx;" class="no-img" mode="widthFix">
								</image>
							</view>
							<view style="margin-left: 20rpx;font-size: 28rpx;font-weight: 400;">
								{{item.issue_names}}
							</view>
						</view>
						<view v-if="item.problem_images!=null && item.problem_images!=''"
							style="text-align: center;margin-top: 20rpx;">
							<image :src="apiImgUrl+item.problem_images[0]"
								style="width: 200rpx;height: 200rpx;border-radius:18rpx 0rpx 0rpx 18rpx;"></image>
							<image :src="apiImgUrl+item.problem_images[1]"
								style="width: 200rpx;height: 200rpx;margin: 0rpx 10rpx;">
							</image>
							<image :src="apiImgUrl+item.problem_images[2]"
								style="width: 200rpx;height: 200rpx;border-radius:0rpx 18rpx 18rpx 0rpx;"></image>
						</view>
						<view v-if="item.answer_content !=''&&item.answer_content!=null" class="tn-flex"
							style="background-color: #F7F7F7;padding: 20rpx;border-radius: 18rpx;margin-top: 20rpx;">
							<view>
								<image src="/static/da.png" style="width: 35rpx;" class="no-img" mode="widthFix">
								</image>
							</view>
							<view class="tn-text-ellipsis-2"
								style="margin-left: 20rpx;font-size: 28rpx;font-weight: 400;color: #9B9B9B;">
								<view v-html="item.answer_content"></view>
							</view>
						</view>
						<view class="tn-flex tn-flex-col-center tn-flex-row-between"
							style="margin-top: 30rpx;font-size: 26rpx;font-weight: 400;color: #9B9B9B;">
							<view>{{item.question_time}}</view>
							<view>
								<text class="tn-icon-eye"></text>
								<text style="margin-left: 5rpx;">{{item.click_count}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<image @click="openUrl('/pages/packageB/ask/add')" src="/static/tiwen.png"
			style="width: 106rpx;position: fixed;right:30rpx;bottom: 150rpx;" mode="widthFix" class="no-img"></image>
	</view>
</template>

<script>
	import {
		getNwwbList
	} from '@/util/api.js';
	import store from '@/store/index.js'

	export default {
		
		data() {
			return {
				top:'none',
				apiImgUrl: store.state.imgUrl,
				list: [],
				page: 1
			}
		},
		onPageScroll(res) {
			console.log(res);
			if (res.scrollTop > 50) {
				this.top = "#ffffff";
			} else {
				this.top = "none";
			}
		},
		onLoad() {
			this.getList();
		},
		onReachBottom() {
			this.page += 1;
			this.getList();
		},
		methods: {
			getList() {
				var user = uni.getStorageSync('userInfo');
				getNwwbList({
						association_id: user.association_id,
						page: this.page,
						size: 10
					})
					.then(res => {
						this.list.push(...res.data)
						console.log(res);
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style>
	.ask_index_bg {
		/* background: url('https://ysx.hschool.com.cn/uploads/1/20250509/4247d991a9a95810641fb3b533602978.png') no-repeat; */
		background: url('https://ysx.0rui.cn/uploads/1/20250509/4247d991a9a95810641fb3b533602978.png') no-repeat;
		background-size: 100%;
		height: 50vh;
	}

	page {
		background-color: #F5F5F5!important;
	}
</style>