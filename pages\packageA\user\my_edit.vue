<template>
	<view class="tn-safe-area-inset-bottom" style="background: rgba(242, 241, 246, 1);">
		<!-- 顶部自定义导航 -->
		<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top" @click="goBack()"
					style="text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'信息修改'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="24"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view style="width: 100%;height: 450rpx;position: absolute;top: 0;">
			<image v-if="addMod.background_image==''" src="/static/my.png" style="width: 100%;height: 450rpx;"></image>
			<image v-if="addMod.background_image!=''" :src="apiImgUrl+addMod.background_image"
				style="width: 100%;height: 450rpx;"></image>
			<view style="bottom: 28rpx;position: absolute;left: 28rpx;">
				<view @click="d_img(1)" style="width: 150rpx;height: 150rpx;position: relative;">
					<image :src="apiImgUrl+addMod.photo_image" mode="aspectFill"
						style="width: 150rpx;height: 150rpx;border-radius: 50%;"></image>
					<image src="/static/pic.png"
						style="height: 50rpx;width: 50rpx;position: absolute;right: 0;bottom: 0;">
					</image>
				</view>
			</view>
			<view style="bottom: 50rpx;right: 28rpx;position: absolute;">
				<tn-button @click="d_img(3)" shape="round" padding="10rpx 40rpx" backgroundColor="#ffffff"
					fontColor="rgba(65, 142, 242, 1);">更改背景</tn-button>
			</view>
		</view>
		<view class="tn-margin-top" style="padding-top: 450rpx;z-index:1;">
			<view style="font-size: 32rpx;color: rgba(119, 119, 119, 1);padding:25rpx 0rpx 30rpx 50rpx;">个人资料</view>
			<view style="padding: 0px 30rpx;;">
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx" @click="openMode(0)">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">姓名</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{addMod.nikename}}</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx"
						@click="position_id_show = true">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">协会职务</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{position_id_name}}</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
					<tn-select v-model="position_id_show" mode="single" :list="position_list"
						@confirm="getpositionId"></tn-select>
				</tn-list-view>
				<!-- <tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx"
						@click="region_id_show = true">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">所属区域</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{region_id_name}}</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
					<tn-select v-model="region_id_show" mode="single" :list="region_list"
						@confirm="getregionId"></tn-select>
				</tn-list-view> -->
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">性别</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center"
							@click="sex_id_show = true">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{addMod.gender==0?'女':'男'}}
							</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
					<tn-select v-model="sex_id_show" mode="single" :list="sex_list" @confirm="getsexId"></tn-select>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx" @click="openMode(1)">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">联系方式</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{addMod.phone}}</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx" @click="openMode(4)">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">个人经历</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view
								style="font-size: 28rpx; color: rgba(102, 102, 102, 1);width: 400rpx;text-align: right;"
								class="tn-text-ellipsis">{{addMod.introduction}}
							</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
				<view style="font-size: 32rpx;color: rgba(119, 119, 119, 1);padding:25rpx 0rpx 30rpx 30rpx;">公司资料</view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx" @click="openMode(2)">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">公司名称</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{addMod.enterprise_name}}
							</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">公司行业</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center"
							@click="industry_id_show = true">
							<view style="font-size: 28rpx; color: rgba(102, 102, 102, 1);">{{industry_id_name}}</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
					<tn-select v-model="industry_id_show" mode="single" :list="industry_list"
						@confirm="getindustryId"></tn-select>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between tn-flex-center tn-flex-col-center"
						style="padding: 30rpx">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">公司Logo</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center" @click="d_img(2)">
							<view>
								<view v-if="addMod.company_image==''">暂无</view>
								<image v-if="addMod.company_image!=''" :src="apiImgUrl+addMod.company_image"
									style="width: 95rpx;" mode="widthFix"></image>
							</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx" @click="openMode(3)">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">公司地址</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view
								style="font-size: 28rpx; color: rgba(102, 102, 102, 1);width: 400rpx;text-align: right;"
								class="tn-text-ellipsis">
								{{addMod.enterprise_location}}
							</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
				<tn-list-view :customTitle="true" backgroundColor="transparent">
					<view slot="title" class="tn-flex tn-flex-row-between" style="padding: 30rpx" @click="openMode(5)">
						<view style="font-size: 32rpx;color: #000000;font-weight: 500;">公司介绍</view>
						<view class="tn-flex tn-flex-row-right tn-flex-center tn-flex-col-center">
							<view
								style="font-size: 28rpx; color: rgba(102, 102, 102, 1);width: 400rpx;text-align: right;"
								class="tn-text-ellipsis">
								{{addMod.enterprise_Introduction}}
							</view>
							<view class="tn-icon-right" style="margin-left: 20rpx;"></view>
						</view>
					</view>
				</tn-list-view>
			</view>
			<view class="tn-flex">
				<view class="tn-flex-1 justify-content-item tn-margin-sm tn-text-center">
					<tn-button backgroundColor="rgb(65, 142, 242)" shape="round" fontColor="#ffffff" padding="40rpx 0"
						width="90%" fontBold @click="setUserEditDo">
						<!-- <text class="tn-icon-light tn-padding-right-xs tn-color-black"></text> -->
						<text>提交审核</text>
					</tn-button>
				</view>
			</view>
		</view>

		<tn-modal v-model="editInput" :custom="true">
			<view style="text-align: center;margin-top: 30rpx;">
				<block v-if="editType==0">
					<view style="text-align: center;font-size: 30rpx;margin-bottom: 20rpx;">姓名修改</view>
					<tn-input :border="true" v-model="addMod.nikename" placeholder="请填写姓名"
						:customStyle="{width:'400rpx'}" />
				</block>
				<block v-if="editType==1">
					<view style="text-align: center;font-size: 30rpx;margin-bottom: 20rpx;">联系方式</view>
					<tn-input :border="true" v-model="addMod.phone" placeholder="请填写联系方式"
						:customStyle="{width:'400rpx'}" />
				</block>
				<block v-if="editType==2">
					<view style="text-align: center;font-size: 30rpx;margin-bottom: 20rpx;">公司名称</view>
					<tn-input :border="true" v-model="addMod.enterprise_name" placeholder="请填写公司名称"
						:customStyle="{width:'400rpx'}" />
				</block>
				<block v-if="editType==3">
					<view style="text-align: center;font-size: 30rpx;margin-bottom: 20rpx;">公司地址</view>
					<tn-input :border="true" v-model="addMod.enterprise_location" placeholder="请填写公司地址"
						:customStyle="{width:'400rpx'}" />
				</block>
			</view>
			<view class="tn-flex tn-flex-row-center" style="margin-top: 50rpx;">
				<tn-button @click="editInput = false" width="250rpx" backgroundColor="#82B2FF"
					fontColor="#ffffff">确定</tn-button>
			</view>
		</tn-modal>
		<tn-popup v-model="editText" mode="right" width="100%">
			<tn-nav-bar :isBack="false" backTitle="" :alpha="true" :bottomShadow="false">
				<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
					<view style="padding-left: 15rpx;" @click="editText = false">
						<text class="tn-icon-left" style="font-size: 40rpx;"></text>
					</view>
					<view class="tn-margin-top"
						style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
						<tn-tabs :list="[{name:editType==4?'编辑个人经历':'编辑公司介绍'}]" :current="topCurrent" activeColor="#000"
							:bold="false" :fontSize="24"></tn-tabs>
					</view>
				</view>
			</tn-nav-bar>
			<view style="background: rgba(242, 241, 246, 1);height: 100%;">
				<view :style="{paddingTop: vuex_custom_bar_height + 20+'px'}"></view>
				<view style="margin:30rpx;background-color: #ffffff;border-radius: 40rpx;">
					<view style="padding: 30rpx;">
						<tn-input v-if="editType==4" :maxLength="1000" v-model="introduction" :clearable="false"
							:height="400" type="textarea" :customStyle="{lineHeight:'42rpx',color:'#777777'}" />
						<tn-input v-if="editType==5" :maxLength="1000" v-model="enterprise_Introduction"
							:clearable="false" :height="400" type="textarea"
							:customStyle="{lineHeight:'42rpx',color:'#777777'}" />
					</view>
				</view>
				<view class="tn-flex tn-flex-center tn-flex-row-center" style="color: #ffffff;">
					<view @click="editText = false"
						style="width: 300rpx;height: 80rpx;background: #d9d9d9;text-align: center;line-height: 80rpx;">
						取消</view>
					<view @click="editTextDo()"
						style="width: 300rpx;height: 80rpx;background: #418EF2;margin-left: 48rpx;text-align: center;line-height: 80rpx;">
						确定</view>
				</view>
			</view>
		</tn-popup>
		<l-clipper v-if="show" :image-url="imageUrl" @success="caiDo($event,1)" @cancel="show = false" />
		<l-clipper v-if="enterprise_show" :image-url="enterpriseImageUrl" @success="caiDo($event,2)"
			@cancel="enterprise_show = false" />
		<l-clipper v-if="back_show" width="750" height="422" max-width="750" max-height="422" :image-url="backImageUrl"
			@success="caiDo($event,3)" @cancel="back_show = false" />
	</view>
</template>

<script>
	import {
		getUserIndex,
		setUserEdit,
		IndustryList,
		getPosition,
		getRegion
	} from "@/util/api";
	import store from "@/store";

	export default {
		data() {
			return {
				editInput: false,
				editText: false,
				show: false,
				enterprise_show: false,
				back_show: false,
				imageUrl: '',
				enterpriseImageUrl: '',
				backImageUrl: '',
				topCurrent: 0,
				showAuthorizationModal: false,
				index: 0,
				date: '2000-01-29',
				userInfo: {},
				editType: '',
				introduction: '',
				enterprise_Introduction: '',
				addMod: {
					photo_image: '',
					member_id: '',
					nikename: '',
					phone: '',
					wx_number: '',
					introduction: '',
					enterprise_name: '',
					enterprise_location: '',
					enterprise_Introduction: '',
					association_id: '',
					industry_id: '',
					company_image: '',
					background_image: '',
					background_type: 3,
					position_id: '',
					gender: 0,
					region_id: '',
				},
				apiImgUrl: this.$store.state.imgUrl,
				apiUpUrl: this.$store.state.apiUrl,
				industry_id_show: false,
				industry_id_name: '',
				industry_list: [],
				position_id_show: false,
				position_id_name: '',
				position_list: [],

				region_id_show: false,
				region_id_name: '',
				region_list: [],

				sex_id_show: false,
				sex_list: [{
					label: '女',
					value: 0
				}, {
					label: '男',
					value: 1
				}],
			}
		},
		onLoad() {
			var uid = uni.getStorageSync('uid');
			this.uid = uid;
			this.getUserInfo();

		},
		methods: {
			openMode(type) {
				this.editType = type;

				if (type < 4) {
					this.editInput = true;
				} else {
					if (type == 4) {
						this.introduction = this.addMod.introduction;
					}
					if (type == 5) {
						this.enterprise_Introduction = this.addMod.enterprise_Introduction;
					}
					this.editText = true;
				}

			},
			editTextDo() {
				if (this.editType == 4) {
					this.addMod.introduction = this.introduction;
				}
				if (this.editType == 5) {
					this.addMod.enterprise_Introduction = this.enterprise_Introduction;
				}
				this.editText = false;
			},
			getindustryId(d) {
				console.log(d);
				var info = d[0];
				this.industry_id_name = info.label;
				this.addMod.industry_id = info.value;
			},
			getpositionId(d) {
				console.log(d);
				var info = d[0];
				this.position_id_name = info.label;
				this.addMod.position_id = info.value;
			},
			getregionId(d) {
				console.log(d);
				var info = d[0];
				this.region_id_name = info.label;
				this.addMod.region_id = info.value;
			},
			getsexId(d) {
				var info = d[0];
				this.addMod.gender = info.value;
			},
			getRegionList() {
				getRegion({
						association_id: this.addMod.association_id,
					})
					.then(res => {
						if (res.code == 1) {
							var key = res.data;
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.region_name
							}));
							this.region_list = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getIndustry() {
				IndustryList({
						association_id: this.addMod.association_id,
					})
					.then(res => {
						if (res.code == 1) {
							var key = res.data;
							var transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.industry_name
							}));
							transformedSelectList.push({
								value: -1,
								label: '其他行业'
							});
							this.industry_list = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getPositionList() {
				getPosition({
						association_id: this.addMod.association_id,
					})
					.then(res => {
						if (res.code == 1) {
							var key = res.data;
							const transformedSelectList = key.map(item => ({
								value: item.id,
								label: item.position_name
							}));
							this.position_list = transformedSelectList;
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			caiDo(d, type) {
				var that = this;
				var url = d.url;
				uni.uploadFile({
					url: that.apiUpUrl + '/common/upload', //仅为示例，非真实的接口地址
					filePath: url,
					name: 'file',
					success: (uploadFileRes) => {
						var data = JSON.parse(uploadFileRes.data);
						console.log(data);
						if (type == 1) {
							that.addMod.photo_image = data.data.url;
							that.show = false;
							that.imageUrl = '';
						}
						if (type == 2) {
							that.addMod.company_image = data.data.url;
							that.enterprise_show = false;
							that.enterpriseImageUrl = '';
						}
						if (type == 3) {
							that.addMod.background_image = data.data.url;
							that.back_show = false;
							that.backImageUrl = '';
						}
					}
				});
			},
			d_img(type) {
				var that = this;
				uni.chooseMedia({
					count: 1, //默认9
					mediaType: ['image', 'video'],
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album'], //从相册选择
					success: function(res) {
						console.log(res);
						var url = res.tempFiles[0].tempFilePath;;
						if (res.type == 'video') {
							uni.showToast({
								title: '暂不支持视频',
								icon: 'none',
								duration: 2000
							});
							return;
						} else {
							that.addMod.background_type = 2;
							if (type == 1) {
								that.imageUrl = url;
								that.show = true;
							}
							if (type == 2) {
								that.enterprise_show = true;
								that.enterpriseImageUrl = url;
							}
							if (type == 3) {
								that.back_show = true;
								that.backImageUrl = url;
							}
						}
					}
				});
			},
			setUserEditDo() {
				setUserEdit(this.addMod)
					.then(res => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '信息已提交审核',
								icon: 'none',
								duration: 2000
							});
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
							});
						}
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			getUserInfo() {
				getUserIndex({
						member_b_id: this.uid,
						member_id: this.uid
					})
					.then(res => {
						console.log(res);
						this.addMod = {
							photo_image: res.data.photo_image,
							member_id: res.data.member_id,
							nikename: res.data.nikename,
							phone: res.data.phone,
							wx_number: res.data.wx_number,
							introduction: res.data.introduction == null || res.data.introduction == '' ? '无' : res
								.data.introduction,
							enterprise_name: res.data.enterprise_name == null || res.data.enterprise_name == '' ?
								'无' : res.data.enterprise_name,
							enterprise_location: res.data.enterprise_location == null || res.data
								.enterprise_location == '' ? '无' : res.data
								.enterprise_location,
							enterprise_Introduction: res.data.enterprise_Introduction == null || res.data
								.enterprise_Introduction == '' ? '无' : res.data
								.enterprise_Introduction,
							association_id: res.data.association_id,
							industry_id: res.data.industry_id,
							company_image: res.data.company_image,
							background_image: res.data.background_image,
							background_type: res.data.background_type,
							position_id: res.data.position_id,
							gender: res.data.gender,
							region_id: res.data.region_id,
						};
						this.region_id_name = res.data.region_name == null ? '无' : res.data.region_name;
						this.industry_id_name = res.data.industry_name == null ? '无' : res.data.industry_name;
						this.position_id_name = res.data.position_name == null ? '无' : res.data.position_name;
						this.getIndustry();
						this.getPositionList();
						this.getRegionList();
					})
					.catch(error => {
						uni.showToast({
							title: error,
							icon: 'none',
							duration: 2000
						});
					})
			},
			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			},

		}
	}
</script>

<style lang="scss" scoped>
	/* 授权 */
	.login-page {
		width: 100vw;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 授权按钮 */
	.submit-btn {
		width: 100%;
		background-color: #05C160;
		color: #FFFFFF;
		margin-top: 60rpx;
		border-radius: 10rpx;
		padding: 25rpx;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 30rpx;
	}

	/* 间隔线 start*/
	.tn-strip-bottom-min {
		width: 100%;
		border-bottom: 1rpx solid #F8F9FB;
	}

	.tn-strip-bottom {
		width: 100%;
		border-bottom: 20rpx solid rgba(241, 241, 241, 0.8);
	}

	/* 间隔线 end*/


	/* 用户头像 start */
	.logo-image {
		width: 80rpx;
		height: 80rpx;
		position: relative;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border: 2rpx solid rgba(255, 255, 255, 0.05);
		box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 50%;
		overflow: hidden;
		// background-color: #FFFFFF;
	}


	/* 底部悬浮按钮 start*/
	.tn-tabbar-height {
		min-height: 100rpx;
		height: calc(120rpx + env(safe-area-inset-bottom) / 2);
	}

	.tn-footerfixed {
		position: fixed;
		width: 100%;
		bottom: calc(30rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);

	}

	/* 底部悬浮按钮 end*/
</style>