<template>
	<view style="letter-spacing: 1rpx;">
		<tn-nav-bar :isBack="false" backTitle="" :bottomShadow="true" backgroundColor="#FFFFFF">
			<view class="custom-nav tn-flex tn-flex-col-center tn-flex-row-left">
				<view style="padding-left: 15rpx;" @click="goBack()">
					<text class="tn-icon-left" style="font-size: 40rpx;"></text>
				</view>
				<view class="tn-margin-top"
					style=";text-shadow:  1rpx 0 0 #FFF, 0 1rpx 0 #FFF, -1rpx 0 0 #FFF , 0 -1rpx 0 #FFF;">
					<tn-tabs :list="[{name:'入会申请记录'}]" :current="topCurrent" activeColor="#000" :bold="false"
						:fontSize="36"></tn-tabs>
				</view>
			</view>
		</tn-nav-bar>
		<view :style="{paddingTop: vuex_custom_bar_height + 20+'px'}">
			<view style="padding:0rpx 30rpx 180rpx 30rpx;">
				<view
					style="position: relative;box-shadow: rgba(12, 0, 5, 0.1) 0px 0px 5px 0px;background-color: #ffffff;"
					@click="openUrl('/pages/index/pizz_info?id='+userInfo.association_id)">
					<view class="tn-flex tn-flex-center tn-flex-col-center"
						style="background-color: #ffffff;padding:60rpx 30rpx;border-radius: 15rpx;margin-bottom: 20rpx;">
						<view>
							<image :src="apiImgUrl+userInfo.association_image" style="width: 100rpx;height: 100rpx;border-radius: 50%;">
							</image>
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 31rpx;font-weight: 600;letter-spacing: 5rpx;">{{userInfo.association_name}}</view>
							<view style="font-size: 24rpx;color: #808080;margin-top: 15rpx;">成立时间：{{userInfo.association_createtime}}</view>
						</view>
					</view>
					<view v-if="userInfo.status==0"
						style="font-size: 20rpx;color: #ffffff;background-color: #FBBD12;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 10rpx;">
						<text class="tn-icon-success-circle-fill" style="vertical-align: middle;"></text>
						<text style="vertical-align: middle;margin-left: 5rpx;">审核中</text>
					</view>
          <view v-if="userInfo.status==1"
                style="font-size: 20rpx;color: #ffffff;background-color: #31C9E8;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 10rpx;">
            <text class="tn-icon-success-circle-fill" style="vertical-align: middle;"></text>
            <text style="vertical-align: middle;margin-left: 5rpx;">正式会员</text>
          </view>
          <view v-if="userInfo.status==2"
                style="font-size: 20rpx;color: #ffffff;background-color: #FF7043;position: absolute;top: 0;right: 0;border-radius: 0px 15rpx 0px 15rpx;padding:10rpx 10rpx;">
            <text class="tn-icon-success-circle-fill" style="vertical-align: middle;"></text>
            <text style="vertical-align: middle;margin-left: 5rpx;">被拒绝</text>
          </view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
import {getUserIndex, loginDo} from "@/util/api";
import store from "@/store";
	export default {
		data() {
			return {
				topCurrent: 0,
        userInfo:{},
        uid:0,
        apiImgUrl:this.$store.state.imgUrl,
			}
		},
    onLoad() {
      var uid = uni.getStorageSync('uid');
      this.uid=uid;
      this.getUserInfo();
    },
		methods: {
      getUserInfo(){
        getUserIndex({member_id:this.uid})
            .then(res => {
              console.log(res);
              if(res.code==1){
                this.userInfo = res.data;
              }
            })
            .catch(error => {
              uni.showToast({
                title: error,
                icon: 'none',
                duration: 2000
              });
            })
      },
			openUrl(e) {
				uni.navigateTo({
					url: e
				})
			},
			goBack() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.redirectTo({
						url: '/pages/index/index'
					})

				}
			}
		}
	}
</script>

<style>

</style>
